#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
错误码定义和错误处理工具
统一管理所有错误码和错误消息格式
"""

from enum import IntEnum
from typing import Dict, Any, Optional
import time
import uuid


class ErrorCode(IntEnum):
    """错误码枚举"""
    
    # 成功状态
    SUCCESS = 0
    
    # 客户端错误 (4000-4999)
    CLIENT_ERROR_BASE = 4000
    INVALID_REQUEST_FORMAT = 4001      # 请求格式错误
    INVALID_AUDIO_FORMAT = 4002        # 音频格式错误
    INVALID_SAMPLE_RATE = 4003         # 采样率错误
    INVALID_DATA_SIZE = 4004           # 数据大小错误
    MISSING_REQUIRED_FIELD = 4005      # 缺少必需字段
    INVALID_PACKET_INDEX = 4006        # 数据包索引错误
    PACKET_TIMEOUT = 4007              # 数据包超时
    INVALID_CLIENT_ID = 4008           # 客户端ID错误
    FEATURE_EXTRACTION_ERROR = 4009    # 特征提取错误
    HEARTBEAT_TIMEOUT = 4010           # 心跳超时
    JSON_DECODE_ERROR = 4011           # JSON解码错误
    CONNECTION_LIMIT_EXCEEDED = 4012   # 连接数超限
    
    # 服务器错误 (5000-5999)
    SERVER_ERROR_BASE = 5000
    DECODE_ERROR = 5001                # 解码错误
    MODEL_LOAD_ERROR = 5002            # 模型加载错误
    LID_ERROR = 5003                   # 语种识别错误
    ONNX_SESSION_ERROR = 5004          # ONNX会话错误
    MEMORY_ERROR = 5005                # 内存错误
    CONFIG_ERROR = 5006                # 配置错误
    INTERNAL_ERROR = 5007              # 内部错误
    SERVICE_UNAVAILABLE = 5008         # 服务不可用
    LANGUAGE_NOT_SUPPORTED = 5009      # 语种不支持
    SESSION_POOL_EXHAUSTED = 5010      # 会话池耗尽
    
    # 系统错误 (6000-6999)
    SYSTEM_ERROR_BASE = 6000
    DISK_SPACE_ERROR = 6001            # 磁盘空间不足
    NETWORK_ERROR = 6002               # 网络错误
    PERMISSION_ERROR = 6003            # 权限错误
    LICENSE_ERROR = 6004               # 授权错误


class ErrorMessage:
    """错误消息定义"""
    
    MESSAGES = {
        ErrorCode.SUCCESS: "操作成功",
        
        # 客户端错误
        ErrorCode.INVALID_REQUEST_FORMAT: "请求格式错误",
        ErrorCode.INVALID_AUDIO_FORMAT: "音频格式错误",
        ErrorCode.INVALID_SAMPLE_RATE: "采样率不支持, 支持的采样率: {supported_rates}",
        ErrorCode.INVALID_DATA_SIZE: "数据包大小错误, 期望: {expected}, 实际: {actual}",
        ErrorCode.MISSING_REQUIRED_FIELD: "缺少必需字段: {field}",
        ErrorCode.INVALID_PACKET_INDEX: "数据包索引错误, 期望: {expected}, 实际: {actual}",
        ErrorCode.PACKET_TIMEOUT: "数据包接收超时",
        ErrorCode.INVALID_CLIENT_ID: "客户端ID无效",
        ErrorCode.FEATURE_EXTRACTION_ERROR: "特征提取失败: {details}",
        ErrorCode.HEARTBEAT_TIMEOUT: "心跳超时, 未在{timeout}秒内接收到数据包",
        ErrorCode.JSON_DECODE_ERROR: "JSON数据解析失败",
        ErrorCode.CONNECTION_LIMIT_EXCEEDED: "连接数超过限制: {limit}",
        
        # 服务器错误
        ErrorCode.DECODE_ERROR: "语音解码失败",
        ErrorCode.MODEL_LOAD_ERROR: "模型加载失败: {model_path}",
        ErrorCode.LID_ERROR: "语种识别失败: {details}",
        ErrorCode.ONNX_SESSION_ERROR: "ONNX会话错误: {details}",
        ErrorCode.MEMORY_ERROR: "内存不足",
        ErrorCode.CONFIG_ERROR: "配置错误: {details}",
        ErrorCode.INTERNAL_ERROR: "内部服务器错误",
        ErrorCode.SERVICE_UNAVAILABLE: "服务暂时不可用",
        ErrorCode.LANGUAGE_NOT_SUPPORTED: "不支持的语种: {language}",
        ErrorCode.SESSION_POOL_EXHAUSTED: "会话池已耗尽, 请稍后重试",
        
        # 系统错误
        ErrorCode.DISK_SPACE_ERROR: "磁盘空间不足",
        ErrorCode.NETWORK_ERROR: "网络连接错误",
        ErrorCode.PERMISSION_ERROR: "权限不足",
        ErrorCode.LICENSE_ERROR: "授权验证失败",
    }
    
    @classmethod
    def get_message(cls, error_code: ErrorCode, **kwargs) -> str:
        """
        获取错误消息
        Args:
            error_code: 错误码
            **kwargs: 消息格式化参数
        Returns:
            str: 格式化后的错误消息
        """
        message_template = cls.MESSAGES.get(error_code, f"未知错误: {error_code}")
        try:
            return message_template.format(**kwargs)
        except KeyError as e:
            return f"{message_template} (格式化参数缺失: {e})"


class ErrorResponse:
    """标准错误响应格式"""
    
    @staticmethod
    def create_error_response(
        error_code: ErrorCode,
        client_id: str,
        index: int = 0,
        message_id: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        创建标准错误响应
        Args:
            error_code: 错误码
            client_id: 客户端ID
            index: 消息索引
            message_id: 消息ID
            **kwargs: 错误消息格式化参数
        Returns:
            Dict: 标准错误响应
        """
        if message_id is None:
            message_id = f"{client_id}_{uuid.uuid4()}"
        
        return {
            "code": int(error_code),
            "state": "error",
            "index": index,
            "result": ErrorMessage.get_message(error_code, **kwargs),
            "voice_id": client_id,
            "message_id": message_id,
            "timestamp": int(time.time() * 1000),
            "final": 1
        }
    
    @staticmethod
    def create_success_response(
        client_id: str,
        result: str,
        index: int = 0,
        message_id: Optional[str] = None,
        is_final: bool = False,
        language: Optional[str] = None,
        language_confidence: Optional[float] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        创建标准成功响应
        Args:
            client_id: 客户端ID
            result: 识别结果
            index: 消息索引
            message_id: 消息ID
            is_final: 是否为最终结果
            language: 识别的语种
            language_confidence: 语种置信度
            **kwargs: 其他响应字段
        Returns:
            Dict: 标准成功响应
        """
        if message_id is None:
            message_id = f"{client_id}_{uuid.uuid4()}"
        
        response = {
            "code": int(ErrorCode.SUCCESS),
            "state": "success",
            "index": index,
            "result": result,
            "voice_id": client_id,
            "message_id": message_id,
            "timestamp": int(time.time() * 1000),
            "final": 1 if is_final else 0
        }
        
        # 添加语种信息（如果可用）
        if language is not None:
            response["language"] = language
        if language_confidence is not None:
            response["language_confidence"] = language_confidence
        
        # 添加其他字段
        response.update(kwargs)
        
        return response


def is_client_error(error_code: ErrorCode) -> bool:
    """判断是否为客户端错误"""
    return ErrorCode.CLIENT_ERROR_BASE <= error_code < ErrorCode.SERVER_ERROR_BASE


def is_server_error(error_code: ErrorCode) -> bool:
    """判断是否为服务器错误"""
    return ErrorCode.SERVER_ERROR_BASE <= error_code < ErrorCode.SYSTEM_ERROR_BASE


def is_system_error(error_code: ErrorCode) -> bool:
    """判断是否为系统错误"""
    return error_code >= ErrorCode.SYSTEM_ERROR_BASE


def get_error_category(error_code: ErrorCode) -> str:
    """获取错误类别"""
    if is_client_error(error_code):
        return "CLIENT_ERROR"
    elif is_server_error(error_code):
        return "SERVER_ERROR"
    elif is_system_error(error_code):
        return "SYSTEM_ERROR"
    else:
        return "UNKNOWN_ERROR"
