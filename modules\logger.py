import os, sys
from typing import Dict, Any
from loguru import logger
from pathlib import Path

def get_folder_path(lang_code: str = None) -> str:
    """
    Get the absolute path of the directory containing the current script.
    Args:
        lang_code: 语种代码, 用于创建特定的日志目录
    Returns:
        str: The absolute path of the directory where the script is located.
    """
    if lang_code is None:
        # 尝试从命令行参数获取
        lang_code = sys.argv[1] if len(sys.argv) > 1 else "default"

    log_dir = Path(os.path.dirname(os.path.abspath(sys.argv[0]))) / f"log_{lang_code}"
    log_dir.mkdir(exist_ok=True)  # 确保日志目录存在
    return str(log_dir) + "/"


def create_log(log_config: Dict[str, Any], log_folder: str, debug_mode: bool = False):
    """
    Create a loguru Logger instance with configuration-based settings.
    Args:
        log_config: 日志配置字典
        log_folder: 日志文件夹路径
        debug_mode: 是否启用调试模式（在终端显示日志）
    Returns:
        A configured loguru Logger instance.
    """
    level = log_config.get('level', 'INFO')
    prefix = f"server-{level}."
    rotation = log_config.get('rotation', '1 day')
    retention = log_config.get('retention', '7 days')
    compression = log_config.get('compression', 'zip')
    max_file_size = log_config.get('max_file_size', '100 MB')   # TODO 参数未使用
    encoding = "utf-8"
    backtrace = False
    diagnose = False
    format_str = "<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | <level>{level: <5}</level> | <cyan>{name:7}</cyan>:<cyan>{function:12}</cyan>:<yellow>{line}</yellow> - <level>{message}</level>"

    # 根据debug_mode决定是否在终端显示日志
    if not debug_mode:
        logger.remove()   # 不在终端打印日志

    logger.add((log_folder + prefix + "{time}.log"),
      format=format_str,
      rotation=rotation,
      retention=retention,
      compression=compression,
      encoding=encoding,
      level=level,
      backtrace=backtrace,
      diagnose=diagnose)
    return logger


def init_logger_from_config(log_config: Dict[str, Any], lang_code: str = None, debug_mode: bool = False):
    """
    从配置初始化日志系统
    Args:
        log_config: 日志配置字典
        lang_code: 语种代码
        debug_mode: 是否启用调试模式
    Returns:
        配置好的logger实例
    """
    folder = get_folder_path(lang_code)
    return create_log(log_config, folder, debug_mode)


def get_default_logger(debug_mode=True):
    """
    获取默认的logger实例（向后兼容）
    """
    # 默认配置
    default_config = {
        'level': 'INFO',
        'rotation': '1 day',
        'retention': '7 days',
        'compression': 'zip',
        'max_file_size': '100 MB'
    }

    folder = get_folder_path()
    return create_log(default_config, folder, debug_mode)


# 默认logger实例（向后兼容）
logger = get_default_logger()
