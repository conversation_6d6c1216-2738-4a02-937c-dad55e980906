2025-07-08 07:53:14.896 | INFO  | modules.config:init_logger :594 - 日志系统初始化成功, 配置: {'level': 'INFO', 'rotation': '1 day', 'retention': '7 days', 'compression': 'zip', 'max_file_size': '100 MB'}
2025-07-08 07:53:14.925 | INFO  | modules.monitoring:_start_health_server:301 - 健康检查服务器已启动, 端口: 8081
2025-07-08 07:53:14.926 | INFO  | modules.monitoring:__init__    :174 - 系统监控已启用, 内存检查间隔: 60秒
2025-07-08 07:53:14.926 | INFO  | modules.config:init_monitoring:625 - 监控系统初始化成功
2025-07-08 07:53:14.929 | INFO  | modules.onnx_session_pool:__init__    :58 - ONNX会话池已启用, 每个模型最大会话数: 4
2025-07-08 07:53:14.935 | INFO  | modules.config:init_session_pool:651 - ONNX会话池初始化成功
2025-07-08 07:53:14.935 | INFO  | modules.config:init_all_modules:669 - 所有模块初始化完成
2025-07-08 07:53:15.009 | INFO  | modules.asr_manager:__init__    :36 - 初始化统一ASR管理器, 支持语种: ['zh', 'en', 'ru', 'kk', 'kkin', 'ug']
2025-07-08 07:53:15.011 | INFO  | server :lifespan    :83 - 启动多语种ASR服务模式
2025-07-08 07:53:15.013 | INFO  | server :lifespan    :84 - 多语种模式：预加载所有支持的语种模型以确保实时切换性能
2025-07-08 07:53:15.020 | INFO  | modules.asr_manager:load_models :53 - 多语种模式：预加载所有支持的语种模型
2025-07-08 07:53:15.026 | INFO  | modules.decoder:load_onnx   :61 - 加载模型: /ws/MODELS/online_onnx_zh, fp16: False, quant: False, device: cpu
2025-07-08 07:53:16.688 | INFO  | modules.symbol_table:load_dict   :28 - 加载词表: /ws/MODELS/online_onnx_zh/units.txt
2025-07-08 07:53:16.694 | INFO  | modules.symbol_table:__init__    :22 - 启用后处理: 转全小写字母
2025-07-08 07:53:16.695 | INFO  | modules.asr_manager:_load_single_language:143 - 成功加载语种 zh 的ASR模型
2025-07-08 07:53:16.695 | INFO  | modules.decoder:load_onnx   :61 - 加载模型: /ws/MODELS/online_onnx_en, fp16: False, quant: False, device: cpu
2025-07-08 07:53:18.774 | INFO  | modules.symbol_table:load_dict   :28 - 加载词表: /ws/MODELS/online_onnx_en/units.txt
2025-07-08 07:53:18.781 | INFO  | modules.symbol_table:__init__    :22 - 启用后处理: 转全小写字母
2025-07-08 07:53:18.782 | INFO  | modules.symbol_table:__init__    :25 - 启用后处理: 去占位下划线
2025-07-08 07:53:18.782 | INFO  | modules.asr_manager:_load_single_language:143 - 成功加载语种 en 的ASR模型
2025-07-08 07:53:18.782 | INFO  | modules.decoder:load_onnx   :61 - 加载模型: /ws/MODELS/online_onnx_ru, fp16: False, quant: True, device: cpu
2025-07-08 07:53:19.244 | INFO  | modules.symbol_table:load_dict   :28 - 加载词表: /ws/MODELS/online_onnx_ru/units.txt
2025-07-08 07:53:19.251 | INFO  | modules.symbol_table:__init__    :22 - 启用后处理: 转全小写字母
2025-07-08 07:53:19.251 | INFO  | modules.symbol_table:__init__    :25 - 启用后处理: 去占位下划线
2025-07-08 07:53:19.251 | INFO  | modules.asr_manager:_load_single_language:143 - 成功加载语种 ru 的ASR模型
2025-07-08 07:53:19.252 | INFO  | modules.decoder:load_onnx   :61 - 加载模型: /ws/MODELS/online_onnx_kk, fp16: False, quant: True, device: cpu
2025-07-08 07:53:19.845 | INFO  | modules.symbol_table:load_dict   :28 - 加载词表: /ws/MODELS/online_onnx_kk/units.txt
2025-07-08 07:53:19.852 | INFO  | modules.symbol_table:__init__    :22 - 启用后处理: 转全小写字母
2025-07-08 07:53:19.852 | INFO  | modules.symbol_table:__init__    :25 - 启用后处理: 去占位下划线
2025-07-08 07:53:19.853 | INFO  | modules.asr_manager:_load_single_language:143 - 成功加载语种 kk 的ASR模型
2025-07-08 07:53:19.853 | INFO  | modules.decoder:load_onnx   :61 - 加载模型: /ws/MODELS/online_onnx_kkin, fp16: False, quant: True, device: cpu
2025-07-08 07:53:20.352 | INFO  | modules.symbol_table:load_dict   :28 - 加载词表: /ws/MODELS/online_onnx_kkin/units.txt
2025-07-08 07:53:20.358 | INFO  | modules.symbol_table:__init__    :13 - 加载映射表: /ws/MODELS/online_onnx_kkin/map_kkin2lat.txt
2025-07-08 07:53:20.359 | INFO  | modules.asr_manager:_load_single_language:143 - 成功加载语种 kkin 的ASR模型
2025-07-08 07:53:20.359 | INFO  | modules.decoder:load_onnx   :61 - 加载模型: /ws/MODELS/online_onnx_ug, fp16: False, quant: True, device: cpu
2025-07-08 07:53:20.816 | INFO  | modules.symbol_table:load_dict   :28 - 加载词表: /ws/MODELS/online_onnx_ug/units.txt
2025-07-08 07:53:20.823 | INFO  | modules.symbol_table:__init__    :13 - 加载映射表: /ws/MODELS/online_onnx_ug/map_uyg2lat.txt
2025-07-08 07:53:20.823 | INFO  | modules.asr_manager:_load_single_language:143 - 成功加载语种 ug 的ASR模型
2025-07-08 07:53:20.823 | INFO  | modules.asr_manager:load_models :69 - 模型加载完成：成功 6/6 个语种
2025-07-08 07:53:20.824 | INFO  | server :lifespan    :90 - 多语种模式启动成功，所有语种模型已预加载
2025-07-08 07:53:20.895 | INFO  | modules.lid_manager:_load_lid_model:95 - LID模型加载成功: /ws/MODELS/lid_model/lid.onnx
2025-07-08 07:53:20.897 | INFO  | modules.lid_manager:_load_lid_model:105 - LID语种字典加载成功: /ws/MODELS/lid_model/spk2id.json
2025-07-08 07:53:20.897 | INFO  | modules.lid_manager:_load_lid_model:114 - 应用全局CMVN: /ws/MODELS/lid_model/global_cmvn
2025-07-08 07:53:20.897 | INFO  | modules.lid_manager:__init__    :71 - LID管理器初始化完成
2025-07-08 07:53:20.897 | INFO  | server :lifespan    :106 - LID管理器初始化成功: /ws/MODELS/lid_model/lid.onnx
2025-07-08 07:53:20.898 | INFO  | server :lifespan    :118 - Server start, init manager, LID_MANAGER, ASR_MANAGER
2025-07-08 07:53:30.890 | INFO  | server :websocket_endpoint:233 - 新建客户连接 "client_id": "111"

2025-07-08 07:53:30.892 | INFO  | server :websocket_endpoint:233 - 新建客户连接 "client_id": "333"

2025-07-08 07:53:30.893 | INFO  | server :websocket_endpoint:233 - 新建客户连接 "client_id": "666"

2025-07-08 07:53:30.894 | INFO  | server :websocket_endpoint:233 - 新建客户连接 "client_id": "444"

2025-07-08 07:53:30.895 | INFO  | server :websocket_endpoint:233 - 新建客户连接 "client_id": "222"

2025-07-08 07:53:30.896 | INFO  | server :websocket_endpoint:233 - 新建客户连接 "client_id": "555"

2025-07-08 07:53:30.897 | INFO  | server :websocket_endpoint:233 - 新建客户连接 "client_id": "777"

2025-07-08 07:53:31.061 | INFO  | modules.connect:on_check    :403 - client_id:111 - 设置自定义分隔符: ", "
2025-07-08 07:53:31.062 | INFO  | modules.connect:_handle_language_options:152 - client_id:111 - 客户端指定语种: en, 跳过LID
2025-07-08 07:53:31.064 | INFO  | modules.connect:_init_decoder:128 - client_id:111 - 初始化解码器, 使用默认语种: zh
2025-07-08 07:53:31.065 | INFO  | modules.asr_manager:switch_to_language:176 - 成功切换到语种: en
2025-07-08 07:53:31.066 | INFO  | modules.connect:_switch_asr_model:802 - client_id:111 - 成功切换到语种: en
2025-07-08 07:53:31.076 | INFO  | modules.decoder:__del__     :149 - Encoder 显式释放资源
2025-07-08 07:53:31.076 | INFO  | modules.decoder:__del__     :411 - ASRDecoder 显式释放资源
2025-07-08 07:53:31.231 | INFO  | modules.connect:on_check    :403 - client_id:333 - 设置自定义分隔符: ", "
2025-07-08 07:53:31.232 | INFO  | modules.connect:_handle_language_options:152 - client_id:333 - 客户端指定语种: en, 跳过LID
2025-07-08 07:53:31.233 | INFO  | modules.connect:_init_decoder:128 - client_id:333 - 初始化解码器, 使用默认语种: zh
2025-07-08 07:53:31.292 | INFO  | modules.connect:_init_decoder:135 - client_id:333 - 解码器初始化完成, 分隔符: ", "
2025-07-08 07:53:31.303 | INFO  | modules.connect:on_check    :557 - client_id:333 - >>> [解析] 第0个数据包, 累计帧数: 38
2025-07-08 07:53:31.304 | INFO  | modules.asr_manager:switch_to_language:176 - 成功切换到语种: en
2025-07-08 07:53:31.304 | INFO  | modules.connect:_switch_asr_model:799 - client_id:333 - 重置解码器以使用新语种配置
2025-07-08 07:53:31.304 | INFO  | modules.decoder:__del__     :411 - ASRDecoder 显式释放资源
2025-07-08 07:53:31.305 | INFO  | modules.decoder:__del__     :149 - Encoder 显式释放资源
2025-07-08 07:53:31.305 | INFO  | modules.decoder:__del__     :267 - CTCPrefixBeamSearch 显式释放资源
2025-07-08 07:53:31.306 | INFO  | modules.connect:_switch_asr_model:802 - client_id:333 - 成功切换到语种: en
2025-07-08 07:53:31.434 | INFO  | modules.connect:on_check    :403 - client_id:666 - 设置自定义分隔符: ", "
2025-07-08 07:53:31.435 | INFO  | modules.connect:_handle_language_options:152 - client_id:666 - 客户端指定语种: en, 跳过LID
2025-07-08 07:53:31.436 | INFO  | modules.connect:_init_decoder:128 - client_id:666 - 初始化解码器, 使用默认语种: zh
2025-07-08 07:53:31.439 | INFO  | modules.connect:_init_decoder:135 - client_id:666 - 解码器初始化完成, 分隔符: ", "
2025-07-08 07:53:31.446 | INFO  | modules.connect:on_check    :557 - client_id:666 - >>> [解析] 第0个数据包, 累计帧数: 38
2025-07-08 07:53:31.448 | INFO  | modules.asr_manager:switch_to_language:176 - 成功切换到语种: en
2025-07-08 07:53:31.448 | INFO  | modules.connect:_switch_asr_model:799 - client_id:666 - 重置解码器以使用新语种配置
2025-07-08 07:53:31.449 | INFO  | modules.decoder:__del__     :411 - ASRDecoder 显式释放资源
2025-07-08 07:53:31.450 | INFO  | modules.decoder:__del__     :149 - Encoder 显式释放资源
2025-07-08 07:53:31.451 | INFO  | modules.decoder:__del__     :267 - CTCPrefixBeamSearch 显式释放资源
2025-07-08 07:53:31.451 | INFO  | modules.connect:_switch_asr_model:802 - client_id:666 - 成功切换到语种: en
2025-07-08 07:53:31.604 | INFO  | modules.connect:on_check    :403 - client_id:444 - 设置自定义分隔符: ", "
2025-07-08 07:53:31.605 | INFO  | modules.connect:_handle_language_options:152 - client_id:444 - 客户端指定语种: en, 跳过LID
2025-07-08 07:53:31.606 | INFO  | modules.connect:_init_decoder:128 - client_id:444 - 初始化解码器, 使用默认语种: zh
2025-07-08 07:53:31.609 | INFO  | modules.connect:_init_decoder:135 - client_id:444 - 解码器初始化完成, 分隔符: ", "
2025-07-08 07:53:31.614 | INFO  | modules.connect:on_check    :557 - client_id:444 - >>> [解析] 第0个数据包, 累计帧数: 38
2025-07-08 07:53:31.615 | INFO  | modules.asr_manager:switch_to_language:176 - 成功切换到语种: en
2025-07-08 07:53:31.616 | INFO  | modules.connect:_switch_asr_model:799 - client_id:444 - 重置解码器以使用新语种配置
2025-07-08 07:53:31.616 | INFO  | modules.decoder:__del__     :411 - ASRDecoder 显式释放资源
2025-07-08 07:53:31.617 | INFO  | modules.decoder:__del__     :149 - Encoder 显式释放资源
2025-07-08 07:53:31.618 | INFO  | modules.decoder:__del__     :267 - CTCPrefixBeamSearch 显式释放资源
2025-07-08 07:53:31.618 | INFO  | modules.connect:_switch_asr_model:802 - client_id:444 - 成功切换到语种: en
2025-07-08 07:53:31.762 | INFO  | modules.connect:on_check    :403 - client_id:222 - 设置自定义分隔符: ", "
2025-07-08 07:53:31.771 | INFO  | modules.connect:_handle_language_options:152 - client_id:222 - 客户端指定语种: en, 跳过LID
2025-07-08 07:53:31.772 | INFO  | modules.connect:_init_decoder:128 - client_id:222 - 初始化解码器, 使用默认语种: zh
2025-07-08 07:53:31.775 | INFO  | modules.connect:_init_decoder:135 - client_id:222 - 解码器初始化完成, 分隔符: ", "
2025-07-08 07:53:31.779 | INFO  | modules.connect:on_check    :557 - client_id:222 - >>> [解析] 第0个数据包, 累计帧数: 38
2025-07-08 07:53:31.781 | INFO  | modules.asr_manager:switch_to_language:176 - 成功切换到语种: en
2025-07-08 07:53:31.782 | INFO  | modules.connect:_switch_asr_model:799 - client_id:222 - 重置解码器以使用新语种配置
2025-07-08 07:53:31.782 | INFO  | modules.decoder:__del__     :411 - ASRDecoder 显式释放资源
2025-07-08 07:53:31.783 | INFO  | modules.decoder:__del__     :149 - Encoder 显式释放资源
2025-07-08 07:53:31.784 | INFO  | modules.decoder:__del__     :267 - CTCPrefixBeamSearch 显式释放资源
2025-07-08 07:53:31.784 | INFO  | modules.connect:_switch_asr_model:802 - client_id:222 - 成功切换到语种: en
2025-07-08 07:53:31.930 | INFO  | modules.connect:on_check    :403 - client_id:555 - 设置自定义分隔符: ", "
2025-07-08 07:53:31.931 | INFO  | modules.connect:_handle_language_options:152 - client_id:555 - 客户端指定语种: en, 跳过LID
2025-07-08 07:53:31.932 | INFO  | modules.connect:_init_decoder:128 - client_id:555 - 初始化解码器, 使用默认语种: zh
2025-07-08 07:53:31.935 | INFO  | modules.connect:_init_decoder:135 - client_id:555 - 解码器初始化完成, 分隔符: ", "
2025-07-08 07:53:31.940 | INFO  | modules.connect:on_check    :557 - client_id:555 - >>> [解析] 第0个数据包, 累计帧数: 38
2025-07-08 07:53:31.942 | INFO  | modules.asr_manager:switch_to_language:176 - 成功切换到语种: en
2025-07-08 07:53:31.943 | INFO  | modules.connect:_switch_asr_model:799 - client_id:555 - 重置解码器以使用新语种配置
2025-07-08 07:53:31.943 | INFO  | modules.decoder:__del__     :411 - ASRDecoder 显式释放资源
2025-07-08 07:53:31.944 | INFO  | modules.decoder:__del__     :149 - Encoder 显式释放资源
2025-07-08 07:53:31.945 | INFO  | modules.decoder:__del__     :267 - CTCPrefixBeamSearch 显式释放资源
2025-07-08 07:53:31.946 | INFO  | modules.connect:_switch_asr_model:802 - client_id:555 - 成功切换到语种: en
2025-07-08 07:53:32.072 | INFO  | modules.connect:on_check    :403 - client_id:777 - 设置自定义分隔符: ", "
2025-07-08 07:53:32.073 | INFO  | modules.connect:_handle_language_options:152 - client_id:777 - 客户端指定语种: en, 跳过LID
2025-07-08 07:53:32.074 | INFO  | modules.connect:_init_decoder:128 - client_id:777 - 初始化解码器, 使用默认语种: zh
2025-07-08 07:53:32.077 | INFO  | modules.connect:_init_decoder:135 - client_id:777 - 解码器初始化完成, 分隔符: ", "
2025-07-08 07:53:32.082 | INFO  | modules.connect:on_check    :557 - client_id:777 - >>> [解析] 第0个数据包, 累计帧数: 38
2025-07-08 07:53:32.085 | INFO  | modules.asr_manager:switch_to_language:176 - 成功切换到语种: en
2025-07-08 07:53:32.085 | INFO  | modules.connect:_switch_asr_model:799 - client_id:777 - 重置解码器以使用新语种配置
2025-07-08 07:53:32.086 | INFO  | modules.decoder:__del__     :411 - ASRDecoder 显式释放资源
2025-07-08 07:53:32.087 | INFO  | modules.decoder:__del__     :149 - Encoder 显式释放资源
2025-07-08 07:53:32.088 | INFO  | modules.decoder:__del__     :267 - CTCPrefixBeamSearch 显式释放资源
2025-07-08 07:53:32.089 | INFO  | modules.connect:_switch_asr_model:802 - client_id:777 - 成功切换到语种: en
2025-07-08 07:53:32.094 | INFO  | modules.connect:_init_decoder:121 - client_id:333 - 初始化解码器, 使用语种: en
2025-07-08 07:53:32.097 | INFO  | modules.connect:_init_decoder:135 - client_id:333 - 解码器初始化完成, 分隔符: ", "
2025-07-08 07:53:32.101 | INFO  | modules.connect:on_check    :557 - client_id:333 - >>> [解析] 第1个数据包, 累计帧数: 76
2025-07-08 07:53:32.102 | INFO  | modules.connect:_init_decoder:121 - client_id:666 - 初始化解码器, 使用语种: en
2025-07-08 07:53:32.105 | INFO  | modules.connect:_init_decoder:135 - client_id:666 - 解码器初始化完成, 分隔符: ", "
2025-07-08 07:53:32.107 | INFO  | modules.connect:on_check    :557 - client_id:666 - >>> [解析] 第1个数据包, 累计帧数: 76
2025-07-08 07:53:32.108 | INFO  | modules.connect:_init_decoder:121 - client_id:444 - 初始化解码器, 使用语种: en
2025-07-08 07:53:32.110 | INFO  | modules.connect:_init_decoder:135 - client_id:444 - 解码器初始化完成, 分隔符: ", "
2025-07-08 07:53:32.113 | INFO  | modules.connect:on_check    :557 - client_id:444 - >>> [解析] 第1个数据包, 累计帧数: 76
2025-07-08 07:53:32.169 | INFO  | modules.connect:_init_decoder:121 - client_id:222 - 初始化解码器, 使用语种: en
2025-07-08 07:53:32.174 | INFO  | modules.connect:_init_decoder:135 - client_id:222 - 解码器初始化完成, 分隔符: ", "
2025-07-08 07:53:32.180 | INFO  | modules.connect:on_check    :557 - client_id:222 - >>> [解析] 第1个数据包, 累计帧数: 76
2025-07-08 07:53:32.338 | INFO  | modules.connect:_init_decoder:121 - client_id:555 - 初始化解码器, 使用语种: en
2025-07-08 07:53:32.343 | INFO  | modules.connect:_init_decoder:135 - client_id:555 - 解码器初始化完成, 分隔符: ", "
2025-07-08 07:53:32.349 | INFO  | modules.connect:on_check    :557 - client_id:555 - >>> [解析] 第1个数据包, 累计帧数: 76
2025-07-08 07:53:32.477 | INFO  | modules.connect:_init_decoder:121 - client_id:777 - 初始化解码器, 使用语种: en
2025-07-08 07:53:32.482 | INFO  | modules.connect:_init_decoder:135 - client_id:777 - 解码器初始化完成, 分隔符: ", "
2025-07-08 07:53:32.489 | INFO  | modules.connect:on_check    :557 - client_id:777 - >>> [解析] 第1个数据包, 累计帧数: 76
2025-07-08 07:53:32.498 | INFO  | modules.connect:on_check    :557 - client_id:333 - >>> [解析] 第2个数据包, 累计帧数: 114
2025-07-08 07:53:32.794 | INFO  | modules.connect:on_check    :557 - client_id:666 - >>> [解析] 第2个数据包, 累计帧数: 114
2025-07-08 07:53:33.075 | INFO  | modules.connect:on_check    :557 - client_id:444 - >>> [解析] 第2个数据包, 累计帧数: 114
2025-07-08 07:53:33.354 | INFO  | modules.connect:on_check    :557 - client_id:222 - >>> [解析] 第2个数据包, 累计帧数: 114
2025-07-08 07:53:33.634 | INFO  | modules.connect:on_check    :557 - client_id:555 - >>> [解析] 第2个数据包, 累计帧数: 114
2025-07-08 07:53:33.914 | INFO  | modules.connect:on_result   :366 - client_id:555 - <<< [发送] 第0个数据包, 更新识别结果: "scientists"
2025-07-08 07:53:33.925 | INFO  | modules.connect:on_check    :557 - client_id:777 - >>> [解析] 第2个数据包, 累计帧数: 114
2025-07-08 07:53:34.203 | INFO  | modules.connect:on_check    :557 - client_id:333 - >>> [解析] 第3个数据包, 累计帧数: 152
2025-07-08 07:53:34.206 | INFO  | modules.connect:on_check    :557 - client_id:666 - >>> [解析] 第3个数据包, 累计帧数: 152
2025-07-08 07:53:34.209 | INFO  | modules.connect:on_check    :557 - client_id:444 - >>> [解析] 第3个数据包, 累计帧数: 152
2025-07-08 07:53:34.212 | INFO  | modules.connect:on_check    :557 - client_id:222 - >>> [解析] 第3个数据包, 累计帧数: 152
2025-07-08 07:53:34.215 | INFO  | modules.connect:on_check    :557 - client_id:555 - >>> [解析] 第3个数据包, 累计帧数: 152
2025-07-08 07:53:34.221 | INFO  | modules.connect:on_check    :557 - client_id:777 - >>> [解析] 第3个数据包, 累计帧数: 152
2025-07-08 07:53:34.224 | INFO  | modules.connect:on_check    :557 - client_id:333 - >>> [解析] 第4个数据包, 累计帧数: 190
2025-07-08 07:53:34.227 | INFO  | modules.connect:on_check    :557 - client_id:666 - >>> [解析] 第4个数据包, 累计帧数: 190
2025-07-08 07:53:34.230 | INFO  | modules.connect:on_check    :557 - client_id:444 - >>> [解析] 第4个数据包, 累计帧数: 190
2025-07-08 07:53:34.232 | INFO  | modules.connect:on_check    :557 - client_id:222 - >>> [解析] 第4个数据包, 累计帧数: 190
2025-07-08 07:53:34.235 | INFO  | modules.connect:on_check    :557 - client_id:555 - >>> [解析] 第4个数据包, 累计帧数: 190
2025-07-08 07:53:34.239 | INFO  | modules.connect:on_check    :557 - client_id:777 - >>> [解析] 第4个数据包, 累计帧数: 190
2025-07-08 07:53:34.241 | INFO  | modules.connect:on_check    :557 - client_id:333 - >>> [解析] 第5个数据包, 累计帧数: 228
2025-07-08 07:53:34.992 | INFO  | modules.connect:on_result   :366 - client_id:333 - <<< [发送] 第0个数据包, 更新识别结果: "the"
2025-07-08 07:53:34.995 | INFO  | modules.connect:on_check    :557 - client_id:666 - >>> [解析] 第5个数据包, 累计帧数: 228
2025-07-08 07:53:35.759 | INFO  | modules.connect:on_result   :366 - client_id:666 - <<< [发送] 第0个数据包, 更新识别结果: "similarly"
2025-07-08 07:53:35.764 | INFO  | modules.connect:on_check    :557 - client_id:444 - >>> [解析] 第5个数据包, 累计帧数: 228
2025-07-08 07:53:36.526 | INFO  | modules.connect:on_result   :366 - client_id:444 - <<< [发送] 第0个数据包, 更新识别结果: "ancient cultures"
2025-07-08 07:53:36.537 | INFO  | modules.connect:on_check    :557 - client_id:222 - >>> [解析] 第5个数据包, 累计帧数: 228
2025-07-08 07:53:37.303 | INFO  | modules.connect:on_result   :366 - client_id:222 - <<< [发送] 第0个数据包, 更新识别结果: "swirl the two"
2025-07-08 07:53:37.307 | INFO  | modules.connect:on_check    :557 - client_id:555 - >>> [解析] 第5个数据包, 累计帧数: 228
2025-07-08 07:53:37.863 | INFO  | modules.connect:on_result   :366 - client_id:555 - <<< [发送] 第1个数据包, 更新识别结果: "scientists hope to understand how"
2025-07-08 07:53:37.893 | INFO  | modules.connect:on_check    :557 - client_id:777 - >>> [解析] 第5个数据包, 累计帧数: 228
2025-07-08 07:53:38.662 | INFO  | modules.connect:on_result   :366 - client_id:777 - <<< [发送] 第0个数据包, 更新识别结果: "aces"
2025-07-08 07:53:38.666 | INFO  | modules.connect:on_check    :557 - client_id:333 - >>> [解析] 第6个数据包, 累计帧数: 266
2025-07-08 07:53:38.669 | INFO  | modules.connect:on_check    :557 - client_id:666 - >>> [解析] 第6个数据包, 累计帧数: 266
2025-07-08 07:53:38.672 | INFO  | modules.connect:on_check    :557 - client_id:444 - >>> [解析] 第6个数据包, 累计帧数: 266
2025-07-08 07:53:38.675 | INFO  | modules.connect:on_check    :557 - client_id:222 - >>> [解析] 第6个数据包, 累计帧数: 266
2025-07-08 07:53:38.683 | INFO  | modules.connect:on_check    :557 - client_id:777 - >>> [解析] 第6个数据包, 累计帧数: 266
2025-07-08 07:53:38.686 | INFO  | modules.connect:on_check    :557 - client_id:333 - >>> [解析] 第7个数据包, 累计帧数: 304
2025-07-08 07:53:39.475 | INFO  | modules.connect:on_result   :366 - client_id:333 - <<< [发送] 第1个数据包, 更新识别结果: "the city is also the"
2025-07-08 07:53:39.479 | INFO  | modules.connect:on_check    :557 - client_id:666 - >>> [解析] 第7个数据包, 累计帧数: 304
2025-07-08 07:53:40.260 | INFO  | modules.connect:on_result   :366 - client_id:666 - <<< [发送] 第1个数据包, 更新识别结果: "similarly by having"
2025-07-08 07:53:40.270 | INFO  | modules.connect:on_check    :557 - client_id:444 - >>> [解析] 第7个数据包, 累计帧数: 304
2025-07-08 07:53:41.057 | INFO  | modules.connect:on_result   :366 - client_id:444 - <<< [发送] 第1个数据包, 更新识别结果: "ancient cultures and tribes began to"
2025-07-08 07:53:41.068 | INFO  | modules.connect:on_check    :557 - client_id:222 - >>> [解析] 第7个数据包, 累计帧数: 304
2025-07-08 07:53:41.846 | INFO  | modules.connect:on_result   :366 - client_id:222 - <<< [发送] 第1个数据包, 更新识别结果: "swirl the two dry powers together"
2025-07-08 07:53:41.856 | INFO  | modules.connect:on_check    :557 - client_id:555 - >>> [解析] 第6个数据包, 累计帧数: 266
2025-07-08 07:53:41.868 | INFO  | modules.connect:on_check    :557 - client_id:777 - >>> [解析] 第7个数据包, 累计帧数: 304
2025-07-08 07:53:42.649 | INFO  | modules.connect:on_result   :366 - client_id:777 - <<< [发送] 第1个数据包, 更新识别结果: "aces e pc"
2025-07-08 07:53:42.654 | INFO  | modules.connect:on_check    :557 - client_id:333 - >>> [解析] 第8个数据包, 累计帧数: 342
2025-07-08 07:53:42.657 | INFO  | modules.connect:on_check    :557 - client_id:666 - >>> [解析] 第8个数据包, 累计帧数: 342
2025-07-08 07:53:42.660 | INFO  | modules.connect:on_check    :557 - client_id:444 - >>> [解析] 第8个数据包, 累计帧数: 342
2025-07-08 07:53:42.662 | INFO  | modules.connect:on_check    :557 - client_id:222 - >>> [解析] 第8个数据包, 累计帧数: 342
2025-07-08 07:53:42.665 | INFO  | modules.connect:on_check    :557 - client_id:555 - >>> [解析] 第7个数据包, 累计帧数: 304
2025-07-08 07:53:43.230 | INFO  | modules.connect:on_result   :366 - client_id:555 - <<< [发送] 第2个数据包, 更新识别结果: "scientists hope to understand how plane form especially how"
2025-07-08 07:53:43.238 | INFO  | modules.connect:on_check    :557 - client_id:777 - >>> [解析] 第8个数据包, 累计帧数: 342
2025-07-08 07:53:43.241 | INFO  | modules.connect:on_check    :557 - client_id:333 - >>> [解析] 第9个数据包, 累计帧数: 380
2025-07-08 07:53:43.244 | INFO  | modules.connect:on_check    :557 - client_id:666 - >>> [解析] 第9个数据包, 累计帧数: 380
2025-07-08 07:53:43.246 | INFO  | modules.connect:on_check    :557 - client_id:444 - >>> [解析] 第9个数据包, 累计帧数: 380
2025-07-08 07:53:43.249 | INFO  | modules.connect:on_check    :557 - client_id:222 - >>> [解析] 第9个数据包, 累计帧数: 380
2025-07-08 07:53:43.252 | INFO  | modules.connect:on_check    :557 - client_id:555 - >>> [解析] 第8个数据包, 累计帧数: 342
2025-07-08 07:53:43.256 | INFO  | modules.connect:on_check    :557 - client_id:777 - >>> [解析] 第9个数据包, 累计帧数: 380
2025-07-08 07:53:43.259 | INFO  | modules.connect:on_check    :557 - client_id:333 - >>> [解析] 第10个数据包, 累计帧数: 418
2025-07-08 07:53:43.853 | INFO  | modules.connect:on_result   :366 - client_id:333 - <<< [发送] 第2个数据包, 更新识别结果: "the city is also the base to climb"
2025-07-08 07:53:43.856 | INFO  | modules.connect:on_check    :557 - client_id:666 - >>> [解析] 第10个数据包, 累计帧数: 418
2025-07-08 07:53:44.396 | INFO  | modules.connect:on_result   :366 - client_id:666 - <<< [发送] 第2个数据包, 更新识别结果: "similarly by having a shenzhen"
2025-07-08 07:53:44.399 | INFO  | modules.connect:on_check    :557 - client_id:444 - >>> [解析] 第10个数据包, 累计帧数: 418
2025-07-08 07:53:44.945 | INFO  | modules.connect:on_result   :366 - client_id:444 - <<< [发送] 第2个数据包, 更新识别结果: "ancient cultures and tribes began to keep them for easy"
2025-07-08 07:53:44.948 | INFO  | modules.connect:on_check    :557 - client_id:222 - >>> [解析] 第10个数据包, 累计帧数: 418
2025-07-08 07:53:45.497 | INFO  | modules.connect:on_result   :366 - client_id:222 - <<< [发送] 第2个数据包, 更新识别结果: "swirl the two dry powers together and then with"
2025-07-08 07:53:45.501 | INFO  | modules.connect:on_check    :557 - client_id:555 - >>> [解析] 第9个数据包, 累计帧数: 380
2025-07-08 07:53:45.507 | INFO  | modules.connect:on_check    :557 - client_id:777 - >>> [解析] 第10个数据包, 累计帧数: 418
2025-07-08 07:53:46.053 | INFO  | modules.connect:on_result   :366 - client_id:777 - <<< [发送] 第2个数据包, 更新识别结果: "aces e pc earlier launched world"
2025-07-08 07:53:46.057 | INFO  | modules.connect:on_check    :557 - client_id:333 - >>> [解析] 第11个数据包, 累计帧数: 456
2025-07-08 07:53:46.059 | INFO  | modules.connect:on_check    :557 - client_id:666 - >>> [解析] 第11个数据包, 累计帧数: 456
2025-07-08 07:53:46.062 | INFO  | modules.connect:on_check    :557 - client_id:444 - >>> [解析] 第11个数据包, 累计帧数: 456
2025-07-08 07:53:46.064 | INFO  | modules.connect:on_check    :557 - client_id:222 - >>> [解析] 第11个数据包, 累计帧数: 456
2025-07-08 07:53:46.067 | INFO  | modules.connect:on_check    :557 - client_id:555 - >>> [解析] 第10个数据包, 累计帧数: 418
2025-07-08 07:53:46.609 | INFO  | modules.connect:on_result   :366 - client_id:555 - <<< [发送] 第3个数据包, 更新识别结果: "scientists hope to understand how plane form especially how the earth forms since"
2025-07-08 07:53:46.619 | INFO  | modules.connect:on_check    :557 - client_id:777 - >>> [解析] 第11个数据包, 累计帧数: 456
2025-07-08 07:53:46.622 | INFO  | modules.connect:on_check    :557 - client_id:333 - >>> [解析] 第12个数据包, 累计帧数: 494
2025-07-08 07:53:47.162 | INFO  | modules.connect:on_result   :366 - client_id:333 - <<< [发送] 第3个数据包, 更新识别结果: "the city is also the base to climb the nira"
2025-07-08 07:53:47.165 | INFO  | modules.connect:on_check    :557 - client_id:666 - >>> [解析] 第12个数据包, 累计帧数: 494
2025-07-08 07:53:47.708 | INFO  | modules.connect:on_result   :366 - client_id:666 - <<< [发送] 第3个数据包, 更新识别结果: "similarly by having a shenzhen visa"
2025-07-08 07:53:47.711 | INFO  | modules.connect:on_check    :557 - client_id:444 - >>> [解析] 第12个数据包, 累计帧数: 494
2025-07-08 07:53:48.262 | INFO  | modules.connect:on_result   :366 - client_id:444 - <<< [发送] 第3个数据包, 更新识别结果: "ancient cultures and tribes began to keep them for easy access to milk"
2025-07-08 07:53:48.265 | INFO  | modules.connect:on_check    :557 - client_id:222 - >>> [解析] 第12个数据包, 累计帧数: 494
2025-07-08 07:53:48.812 | INFO  | modules.connect:on_result   :366 - client_id:222 - <<< [发送] 第3个数据包, 更新识别结果: "swirl the two dry powers together and then with clean wet hands"
2025-07-08 07:53:48.815 | INFO  | modules.connect:on_check    :557 - client_id:555 - >>> [解析] 第11个数据包, 累计帧数: 456
2025-07-08 07:53:48.819 | INFO  | modules.connect:on_check    :557 - client_id:777 - >>> [解析] 第12个数据包, 累计帧数: 494
2025-07-08 07:53:49.361 | INFO  | modules.connect:on_result   :366 - client_id:777 - <<< [发送] 第3个数据包, 更新识别结果: "aces e pc earlier launched worldwide for cost sav"
2025-07-08 07:53:49.364 | INFO  | modules.connect:on_check    :557 - client_id:333 - >>> [解析] 第13个数据包, 累计帧数: 532
2025-07-08 07:53:49.367 | INFO  | modules.connect:on_check    :557 - client_id:666 - >>> [解析] 第13个数据包, 累计帧数: 532
2025-07-08 07:53:49.369 | INFO  | modules.connect:on_check    :557 - client_id:444 - >>> [解析] 第13个数据包, 累计帧数: 532
2025-07-08 07:53:49.372 | INFO  | modules.connect:on_check    :557 - client_id:222 - >>> [解析] 第13个数据包, 累计帧数: 532
2025-07-08 07:53:49.374 | INFO  | modules.connect:on_check    :557 - client_id:555 - >>> [解析] 第12个数据包, 累计帧数: 494
2025-07-08 07:53:49.915 | INFO  | modules.connect:on_result   :366 - client_id:555 - <<< [发送] 第4个数据包, 更新识别结果: "scientists hope to understand how plane form especially how the earth forms since comets collided with the"
2025-07-08 07:53:49.919 | INFO  | modules.connect:on_check    :557 - client_id:777 - >>> [解析] 第13个数据包, 累计帧数: 532
2025-07-08 07:53:49.921 | INFO  | modules.connect:on_check    :557 - client_id:333 - >>> [解析] 第14个数据包, 累计帧数: 570
2025-07-08 07:53:49.924 | INFO  | modules.connect:on_check    :557 - client_id:666 - >>> [解析] 第14个数据包, 累计帧数: 570
2025-07-08 07:53:49.926 | INFO  | modules.connect:on_check    :557 - client_id:444 - >>> [解析] 第14个数据包, 累计帧数: 570
2025-07-08 07:53:49.929 | INFO  | modules.connect:on_check    :557 - client_id:222 - >>> [解析] 第14个数据包, 累计帧数: 570
2025-07-08 07:53:49.931 | INFO  | modules.connect:on_check    :557 - client_id:555 - >>> [解析] 第13个数据包, 累计帧数: 532
2025-07-08 07:53:49.935 | INFO  | modules.connect:on_check    :557 - client_id:777 - >>> [解析] 第14个数据包, 累计帧数: 570
2025-07-08 07:53:49.937 | INFO  | modules.connect:on_check    :557 - client_id:333 - >>> [解析] 第15个数据包, 累计帧数: 608
2025-07-08 07:53:50.484 | INFO  | modules.connect:on_result   :366 - client_id:333 - <<< [发送] 第4个数据包, 更新识别结果: "the city is also the base to climb the niraongo volcano"
2025-07-08 07:53:50.487 | INFO  | modules.connect:on_check    :557 - client_id:666 - >>> [解析] 第15个数据包, 累计帧数: 608
2025-07-08 07:53:51.044 | INFO  | modules.connect:on_result   :366 - client_id:666 - <<< [发送] 第4个数据包, 更新识别结果: "similarly by having a shenzhen visa you do not need"
2025-07-08 07:53:51.052 | INFO  | modules.connect:on_check    :557 - client_id:444 - >>> [解析] 第15个数据包, 累计帧数: 608
2025-07-08 07:53:51.598 | INFO  | modules.connect:on_result   :366 - client_id:444 - <<< [发送] 第4个数据包, 更新识别结果: "ancient cultures and tribes began to keep them for easy access to milk hair meat"
2025-07-08 07:53:51.601 | INFO  | modules.connect:on_check    :557 - client_id:222 - >>> [解析] 第15个数据包, 累计帧数: 608
2025-07-08 07:53:52.143 | INFO  | modules.connect:on_result   :366 - client_id:222 - <<< [发送] 第4个数据包, 更新识别结果: "swirl the two dry powers together and then with clean wet hands squeeze"
2025-07-08 07:53:52.146 | INFO  | modules.connect:on_check    :557 - client_id:555 - >>> [解析] 第14个数据包, 累计帧数: 570
2025-07-08 07:53:52.152 | INFO  | modules.connect:on_check    :557 - client_id:777 - >>> [解析] 第15个数据包, 累计帧数: 608
2025-07-08 07:53:52.694 | INFO  | modules.connect:on_result   :366 - client_id:777 - <<< [发送] 第4个数据包, 更新识别结果: "aces e pc earlier launched worldwide for cost saving functionality"
2025-07-08 07:53:52.698 | INFO  | modules.connect:on_check    :557 - client_id:333 - >>> [解析] 第16个数据包, 累计帧数: 646
2025-07-08 07:53:52.701 | INFO  | modules.connect:on_check    :557 - client_id:666 - >>> [解析] 第16个数据包, 累计帧数: 646
2025-07-08 07:53:52.704 | INFO  | modules.connect:on_check    :557 - client_id:444 - >>> [解析] 第16个数据包, 累计帧数: 646
2025-07-08 07:53:52.706 | INFO  | modules.connect:on_check    :557 - client_id:222 - >>> [解析] 第16个数据包, 累计帧数: 646
2025-07-08 07:53:52.710 | INFO  | modules.connect:on_check    :557 - client_id:555 - >>> [解析] 第15个数据包, 累计帧数: 586
2025-07-08 07:53:53.251 | INFO  | modules.decoder:detokenize  :516 - 话音检测: 0.75 s 添加分隔符, 
2025-07-08 07:53:53.488 | INFO  | modules.decoder:detokenize  :516 - 话音检测: 0.75 s 添加分隔符, 
2025-07-08 07:53:53.489 | INFO  | modules.connect:on_decode   :859 - client_id:555 - *** 最后一个数据包完成解码 ***
2025-07-08 07:53:53.489 | INFO  | modules.connect:on_result   :366 - client_id:555 - <<< [发送] 第5个数据包, 更新识别结果: "scientists hope to understand how plane form especially how the earth forms since comets collided with the earth long ago"
2025-07-08 07:53:53.490 | INFO  | server :receive     :313 - client_id:555 - 已发送最后一个识别结果, 主动关闭客户连接
2025-07-08 07:53:53.490 | INFO  | server :receive     :320 - client_id: 555 - 关闭连接，清理资源
2025-07-08 07:53:53.490 | INFO  | modules.connect:disconnect  :269 - 关闭 ws 连接
2025-07-08 07:53:53.506 | INFO  | modules.connect:on_check    :557 - client_id:777 - >>> [解析] 第16个数据包, 累计帧数: 646
2025-07-08 07:53:53.509 | INFO  | modules.connect:on_check    :557 - client_id:333 - >>> [解析] 第17个数据包, 累计帧数: 684
2025-07-08 07:53:54.060 | INFO  | modules.connect:on_result   :366 - client_id:333 - <<< [发送] 第5个数据包, 更新识别结果: "the city is also the base to climb the niraongo volcano along with some of"
2025-07-08 07:53:54.069 | INFO  | modules.connect:on_check    :557 - client_id:666 - >>> [解析] 第17个数据包, 累计帧数: 684
2025-07-08 07:53:54.611 | INFO  | modules.connect:on_result   :366 - client_id:666 - <<< [发送] 第5个数据包, 更新识别结果: "similarly by having a shenzhen visa you do not need to apply"
2025-07-08 07:53:54.614 | INFO  | modules.connect:on_check    :557 - client_id:444 - >>> [解析] 第17个数据包, 累计帧数: 684
2025-07-08 07:53:55.151 | INFO  | modules.connect:on_result   :366 - client_id:444 - <<< [发送] 第5个数据包, 更新识别结果: "ancient cultures and tribes began to keep them for easy access to milk hair meat and skins"
2025-07-08 07:53:55.156 | INFO  | modules.connect:on_check    :557 - client_id:222 - >>> [解析] 第17个数据包, 累计帧数: 684
2025-07-08 07:53:55.691 | INFO  | modules.connect:on_result   :366 - client_id:222 - <<< [发送] 第5个数据包, 更新识别结果: "swirl the two dry powers together and then with clean wet hands squeeze them into a balll"
2025-07-08 07:53:55.696 | INFO  | modules.connect:on_check    :557 - client_id:777 - >>> [解析] 第17个数据包, 累计帧数: 684
2025-07-08 07:53:56.236 | INFO  | modules.connect:on_result   :366 - client_id:777 - <<< [发送] 第5个数据包, 更新识别结果: "aces e pc earlier launched worldwide for cost saving functionality factors became a hot"
2025-07-08 07:53:56.239 | INFO  | modules.connect:on_check    :557 - client_id:333 - >>> [解析] 第18个数据包, 累计帧数: 722
2025-07-08 07:53:56.242 | INFO  | modules.connect:on_check    :557 - client_id:666 - >>> [解析] 第18个数据包, 累计帧数: 722
2025-07-08 07:53:56.245 | INFO  | modules.connect:on_check    :557 - client_id:444 - >>> [解析] 第18个数据包, 累计帧数: 722
2025-07-08 07:53:56.247 | INFO  | modules.connect:on_check    :557 - client_id:222 - >>> [解析] 第18个数据包, 累计帧数: 722
2025-07-08 07:53:56.255 | INFO  | modules.connect:on_check    :557 - client_id:777 - >>> [解析] 第18个数据包, 累计帧数: 722
2025-07-08 07:53:56.258 | INFO  | modules.connect:on_check    :557 - client_id:333 - >>> [解析] 第19个数据包, 累计帧数: 760
2025-07-08 07:53:56.261 | INFO  | modules.connect:on_check    :557 - client_id:666 - >>> [解析] 第19个数据包, 累计帧数: 760
2025-07-08 07:53:56.265 | INFO  | modules.connect:on_check    :557 - client_id:444 - >>> [解析] 第19个数据包, 累计帧数: 740
2025-07-08 07:53:56.725 | INFO  | modules.decoder:detokenize  :516 - 话音检测: 0.75 s 添加分隔符, 
2025-07-08 07:53:56.726 | INFO  | modules.connect:on_decode   :859 - client_id:444 - *** 最后一个数据包完成解码 ***
2025-07-08 07:53:56.726 | INFO  | modules.connect:on_result   :366 - client_id:444 - <<< [发送] 第6个数据包, 更新识别结果: "ancient cultures and tribes began to keep them for easy access to milk hair meat and skins"
2025-07-08 07:53:56.726 | INFO  | server :receive     :313 - client_id:444 - 已发送最后一个识别结果, 主动关闭客户连接
2025-07-08 07:53:56.727 | INFO  | server :receive     :320 - client_id: 444 - 关闭连接，清理资源
2025-07-08 07:53:56.727 | INFO  | modules.connect:disconnect  :269 - 关闭 ws 连接
2025-07-08 07:53:56.730 | INFO  | modules.connect:on_check    :557 - client_id:222 - >>> [解析] 第19个数据包, 累计帧数: 760
2025-07-08 07:53:56.735 | INFO  | modules.connect:on_check    :557 - client_id:777 - >>> [解析] 第19个数据包, 累计帧数: 760
2025-07-08 07:53:56.737 | INFO  | modules.connect:on_check    :557 - client_id:333 - >>> [解析] 第20个数据包, 累计帧数: 798
2025-07-08 07:53:57.277 | INFO  | modules.connect:on_result   :366 - client_id:333 - <<< [发送] 第6个数据包, 更新识别结果: "the city is also the base to climb the niraongo volcano along with some of the cheapest mountain"
2025-07-08 07:53:57.287 | INFO  | modules.connect:on_check    :557 - client_id:666 - >>> [解析] 第20个数据包, 累计帧数: 798
2025-07-08 07:53:57.833 | INFO  | modules.connect:on_result   :366 - client_id:666 - <<< [发送] 第6个数据包, 更新识别结果: "similarly by having a shenzhen visa you do not need to apply for visas"
2025-07-08 07:53:57.840 | INFO  | modules.connect:on_check    :557 - client_id:222 - >>> [解析] 第20个数据包, 累计帧数: 794
2025-07-08 07:53:58.387 | INFO  | modules.decoder:detokenize  :516 - 话音检测: 0.75 s 添加分隔符, 
2025-07-08 07:53:58.713 | INFO  | modules.decoder:detokenize  :516 - 话音检测: 0.75 s 添加分隔符, 
2025-07-08 07:53:58.713 | INFO  | modules.connect:on_decode   :859 - client_id:222 - *** 最后一个数据包完成解码 ***
2025-07-08 07:53:58.714 | INFO  | modules.connect:on_result   :366 - client_id:222 - <<< [发送] 第6个数据包, 更新识别结果: "swirl the two dry powers together and then with clean wet hands, squeeze them into a balll"
2025-07-08 07:53:58.714 | INFO  | server :receive     :313 - client_id:222 - 已发送最后一个识别结果, 主动关闭客户连接
2025-07-08 07:53:58.715 | INFO  | server :receive     :320 - client_id: 222 - 关闭连接，清理资源
2025-07-08 07:53:58.715 | INFO  | modules.connect:disconnect  :269 - 关闭 ws 连接
2025-07-08 07:53:58.716 | INFO  | modules.decoder:__del__     :411 - ASRDecoder 显式释放资源
2025-07-08 07:53:58.716 | INFO  | modules.decoder:__del__     :149 - Encoder 显式释放资源
2025-07-08 07:53:58.717 | INFO  | modules.decoder:__del__     :267 - CTCPrefixBeamSearch 显式释放资源
2025-07-08 07:53:58.723 | INFO  | modules.connect:on_check    :557 - client_id:777 - >>> [解析] 第20个数据包, 累计帧数: 798
2025-07-08 07:53:59.273 | INFO  | modules.connect:on_result   :366 - client_id:777 - <<< [发送] 第6个数据包, 更新识别结果: "aces e pc earlier launched worldwide for cost saving functionality factors became a hot topic in two thousand and seven"
2025-07-08 07:53:59.277 | INFO  | modules.connect:on_check    :557 - client_id:333 - >>> [解析] 第21个数据包, 累计帧数: 836
2025-07-08 07:53:59.280 | INFO  | modules.connect:on_check    :557 - client_id:666 - >>> [解析] 第21个数据包, 累计帧数: 836
2025-07-08 07:53:59.285 | INFO  | modules.connect:on_check    :557 - client_id:777 - >>> [解析] 第21个数据包, 累计帧数: 836
2025-07-08 07:53:59.288 | INFO  | modules.connect:on_check    :557 - client_id:333 - >>> [解析] 第22个数据包, 累计帧数: 874
2025-07-08 07:53:59.844 | INFO  | modules.connect:on_result   :366 - client_id:333 - <<< [发送] 第7个数据包, 更新识别结果: "the city is also the base to climb the niraongo volcano along with some of the cheapest mountain goerrilla traffick"
2025-07-08 07:53:59.848 | INFO  | modules.connect:on_check    :557 - client_id:666 - >>> [解析] 第22个数据包, 累计帧数: 874
2025-07-08 07:54:00.391 | INFO  | modules.connect:on_result   :366 - client_id:666 - <<< [发送] 第7个数据包, 更新识别结果: "similarly by having a shenzhen visa you do not need to apply for visas to each of the she"
2025-07-08 07:54:00.393 | INFO  | modules.decoder:__del__     :411 - ASRDecoder 显式释放资源
2025-07-08 07:54:00.393 | INFO  | modules.decoder:__del__     :149 - Encoder 显式释放资源
2025-07-08 07:54:00.394 | INFO  | modules.decoder:__del__     :267 - CTCPrefixBeamSearch 显式释放资源
2025-07-08 07:54:00.398 | INFO  | modules.connect:on_check    :557 - client_id:777 - >>> [解析] 第22个数据包, 累计帧数: 874
2025-07-08 07:54:00.943 | INFO  | modules.connect:on_result   :366 - client_id:777 - <<< [发送] 第7个数据包, 更新识别结果: "aces e pc earlier launched worldwide for cost saving functionality factors became a hot topic in two thousand and seven tai pi it"
2025-07-08 07:54:00.947 | INFO  | modules.connect:on_check    :557 - client_id:333 - >>> [解析] 第23个数据包, 累计帧数: 912
2025-07-08 07:54:00.950 | INFO  | modules.connect:on_check    :557 - client_id:666 - >>> [解析] 第23个数据包, 累计帧数: 912
2025-07-08 07:54:00.952 | INFO  | modules.decoder:__del__     :411 - ASRDecoder 显式释放资源
2025-07-08 07:54:00.952 | INFO  | modules.decoder:__del__     :149 - Encoder 显式释放资源
2025-07-08 07:54:00.952 | INFO  | modules.decoder:__del__     :267 - CTCPrefixBeamSearch 显式释放资源
2025-07-08 07:54:00.956 | INFO  | modules.connect:on_check    :557 - client_id:777 - >>> [解析] 第23个数据包, 累计帧数: 912
2025-07-08 07:54:00.959 | INFO  | modules.connect:on_check    :557 - client_id:333 - >>> [解析] 第24个数据包, 累计帧数: 950
2025-07-08 07:54:00.962 | INFO  | modules.connect:on_check    :557 - client_id:666 - >>> [解析] 第24个数据包, 累计帧数: 950
2025-07-08 07:54:00.965 | INFO  | modules.connect:on_check    :557 - client_id:777 - >>> [解析] 第24个数据包, 累计帧数: 950
2025-07-08 07:54:00.968 | INFO  | modules.connect:on_check    :557 - client_id:333 - >>> [解析] 第25个数据包, 累计帧数: 988
2025-07-08 07:54:01.514 | INFO  | modules.connect:on_result   :366 - client_id:333 - <<< [发送] 第8个数据包, 更新识别结果: "the city is also the base to climb the niraongo volcano along with some of the cheapest mountain goerrilla trafficking in africa"
2025-07-08 07:54:01.518 | INFO  | modules.connect:on_check    :557 - client_id:666 - >>> [解析] 第25个数据包, 累计帧数: 988
2025-07-08 07:54:02.063 | INFO  | modules.connect:on_result   :366 - client_id:666 - <<< [发送] 第8个数据包, 更新识别结果: "similarly by having a shenzhen visa you do not need to apply for visas to each of the shenzhen member"
2025-07-08 07:54:02.067 | INFO  | modules.connect:on_check    :557 - client_id:777 - >>> [解析] 第25个数据包, 累计帧数: 988
2025-07-08 07:54:02.600 | INFO  | modules.connect:on_result   :366 - client_id:777 - <<< [发送] 第8个数据包, 更新识别结果: "aces e pc earlier launched worldwide for cost saving functionality factors became a hot topic in two thousand and seven tai pi it t month"
2025-07-08 07:54:02.608 | INFO  | modules.connect:on_check    :557 - client_id:333 - >>> [解析] 第26个数据包, 累计帧数: 1026
2025-07-08 07:54:02.611 | INFO  | modules.connect:on_check    :557 - client_id:666 - >>> [解析] 第26个数据包, 累计帧数: 1026
2025-07-08 07:54:02.615 | INFO  | modules.connect:on_check    :557 - client_id:777 - >>> [解析] 第26个数据包, 累计帧数: 1020
2025-07-08 07:54:03.031 | INFO  | modules.decoder:detokenize  :516 - 话音检测: 0.75 s 添加分隔符, 
2025-07-08 07:54:03.032 | INFO  | modules.connect:on_decode   :859 - client_id:777 - *** 最后一个数据包完成解码 ***
2025-07-08 07:54:03.032 | INFO  | modules.connect:on_result   :366 - client_id:777 - <<< [发送] 第9个数据包, 更新识别结果: "aces e pc earlier launched worldwide for cost saving functionality factors became a hot topic in two thousand and seven tai pi it t month"
2025-07-08 07:54:03.033 | INFO  | server :receive     :313 - client_id:777 - 已发送最后一个识别结果, 主动关闭客户连接
2025-07-08 07:54:03.033 | INFO  | server :receive     :320 - client_id: 777 - 关闭连接，清理资源
2025-07-08 07:54:03.033 | INFO  | modules.connect:disconnect  :269 - 关闭 ws 连接
2025-07-08 07:54:03.037 | INFO  | modules.connect:on_check    :557 - client_id:333 - >>> [解析] 第27个数据包, 累计帧数: 1064
2025-07-08 07:54:03.597 | INFO  | modules.connect:on_check    :557 - client_id:666 - >>> [解析] 第27个数据包, 累计帧数: 1064
2025-07-08 07:54:04.135 | INFO  | modules.connect:on_result   :366 - client_id:666 - <<< [发送] 第9个数据包, 更新识别结果: "similarly by having a shenzhen visa you do not need to apply for visas to each of the shenzhen member countries"
2025-07-08 07:54:04.140 | INFO  | modules.connect:on_check    :557 - client_id:333 - >>> [解析] 第28个数据包, 累计帧数: 1070
2025-07-08 07:54:04.438 | INFO  | modules.decoder:detokenize  :516 - 话音检测: 0.75 s 添加分隔符, 
2025-07-08 07:54:04.439 | INFO  | modules.connect:on_decode   :859 - client_id:333 - *** 最后一个数据包完成解码 ***
2025-07-08 07:54:04.439 | INFO  | modules.connect:on_result   :366 - client_id:333 - <<< [发送] 第9个数据包, 更新识别结果: "the city is also the base to climb the niraongo volcano along with some of the cheapest mountain goerrilla trafficking in africa"
2025-07-08 07:54:04.440 | INFO  | server :receive     :313 - client_id:333 - 已发送最后一个识别结果, 主动关闭客户连接
2025-07-08 07:54:04.440 | INFO  | server :receive     :320 - client_id: 333 - 关闭连接，清理资源
2025-07-08 07:54:04.441 | INFO  | modules.connect:disconnect  :269 - 关闭 ws 连接
2025-07-08 07:54:04.445 | INFO  | modules.connect:on_check    :557 - client_id:666 - >>> [解析] 第28个数据包, 累计帧数: 1102
2025-07-08 07:54:04.450 | INFO  | modules.connect:on_check    :557 - client_id:666 - >>> [解析] 第29个数据包, 累计帧数: 1140
2025-07-08 07:54:04.454 | INFO  | modules.connect:on_check    :557 - client_id:666 - >>> [解析] 第30个数据包, 累计帧数: 1178
2025-07-08 07:54:04.980 | INFO  | modules.connect:on_result   :366 - client_id:666 - <<< [发送] 第10个数据包, 更新识别结果: "similarly by having a shenzhen visa you do not need to apply for visas to each of the shenzhen member countries separately and"
2025-07-08 07:54:04.982 | INFO  | modules.decoder:__del__     :411 - ASRDecoder 显式释放资源
2025-07-08 07:54:04.982 | INFO  | modules.decoder:__del__     :149 - Encoder 显式释放资源
2025-07-08 07:54:04.982 | INFO  | modules.decoder:__del__     :267 - CTCPrefixBeamSearch 显式释放资源
2025-07-08 07:54:04.986 | INFO  | modules.connect:on_check    :557 - client_id:666 - >>> [解析] 第31个数据包, 累计帧数: 1216
2025-07-08 07:54:04.988 | INFO  | modules.decoder:__del__     :411 - ASRDecoder 显式释放资源
2025-07-08 07:54:04.988 | INFO  | modules.decoder:__del__     :149 - Encoder 显式释放资源
2025-07-08 07:54:04.989 | INFO  | modules.decoder:__del__     :267 - CTCPrefixBeamSearch 显式释放资源
2025-07-08 07:54:04.992 | INFO  | modules.connect:on_check    :557 - client_id:666 - >>> [解析] 第32个数据包, 累计帧数: 1254
2025-07-08 07:54:05.518 | INFO  | modules.connect:on_result   :366 - client_id:666 - <<< [发送] 第11个数据包, 更新识别结果: "similarly by having a shenzhen visa you do not need to apply for visas to each of the shenzhen member countries separately and saving time money"
2025-07-08 07:54:05.522 | INFO  | modules.connect:on_check    :557 - client_id:666 - >>> [解析] 第33个数据包, 累计帧数: 1292
2025-07-08 07:54:05.527 | INFO  | modules.connect:on_check    :557 - client_id:666 - >>> [解析] 第34个数据包, 累计帧数: 1330
2025-07-08 07:54:05.536 | INFO  | modules.connect:on_check    :557 - client_id:666 - >>> [解析] 第35个数据包, 累计帧数: 1368
2025-07-08 07:54:06.064 | INFO  | modules.connect:on_result   :366 - client_id:666 - <<< [发送] 第12个数据包, 更新识别结果: "similarly by having a shenzhen visa you do not need to apply for visas to each of the shenzhen member countries separately and saving time money and paperwork"
2025-07-08 07:54:06.068 | INFO  | modules.connect:on_check    :557 - client_id:666 - >>> [解析] 第36个数据包, 累计帧数: 1406
2025-07-08 07:54:06.070 | INFO  | modules.connect:on_check    :557 - client_id:666 - >>> [解析] 第37个数据包, 累计帧数: 1408
2025-07-08 07:54:06.522 | INFO  | modules.decoder:detokenize  :516 - 话音检测: 0.75 s 添加分隔符, 
2025-07-08 07:54:06.522 | INFO  | modules.connect:on_decode   :859 - client_id:666 - *** 最后一个数据包完成解码 ***
2025-07-08 07:54:06.522 | INFO  | modules.connect:on_result   :366 - client_id:666 - <<< [发送] 第13个数据包, 更新识别结果: "similarly by having a shenzhen visa you do not need to apply for visas to each of the shenzhen member countries separately and saving time money and paperwork"
2025-07-08 07:54:06.523 | INFO  | server :receive     :313 - client_id:666 - 已发送最后一个识别结果, 主动关闭客户连接
2025-07-08 07:54:06.523 | INFO  | server :receive     :320 - client_id: 666 - 关闭连接，清理资源
2025-07-08 07:54:06.524 | INFO  | modules.connect:disconnect  :269 - 关闭 ws 连接
2025-07-08 07:54:06.527 | INFO  | modules.decoder:__del__     :411 - ASRDecoder 显式释放资源
2025-07-08 07:54:06.527 | INFO  | modules.decoder:__del__     :149 - Encoder 显式释放资源
2025-07-08 07:54:06.531 | INFO  | modules.decoder:__del__     :267 - CTCPrefixBeamSearch 显式释放资源
