#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
ASR管理器
统一管理单语种和多语种的ASR模型, 支持动态切换和实时语种识别
"""

import os
import threading
from typing import Dict, Optional, Any, List
import onnxruntime as ort
from modules.logger import logger
from modules.decoder import load_onnx, ONNX_SESSIONS
from modules.symbol_table import SymbolTable
from modules.config import config_manager
from modules.onnx_session_pool import get_global_session_pool


class ASRManager:
    """统一的ASR管理器, 负责管理各个语种的ASR模型"""

    def __init__(self, global_config):
        """
        初始化ASR管理器

        Args:
            global_config: 全局配置对象
        """
        self.global_config = global_config
        self.supported_languages = global_config.supported_languages
        self.loaded_models = {}  # 存储已加载的模型信息 {lang_code: {sessions, metadatas, config, symbol_table, feat_pipe}}

        # 线程安全锁
        self.model_lock = threading.RLock()
        self.session_pool = get_global_session_pool()

        logger.info("初始化统一ASR管理器, 支持语种: {}".format(self.supported_languages))

    def load_models(self, lang_codes) -> bool:
        """
        根据lang_code加载模型
        Args:
            lang_codes: 语种代码, 可以是字符串或列表
                       - 'multi': 加载所有支持的语种
                       - 'zh': 加载单个语种
                       - ['zh', 'en']: 加载指定的多个语种
        Returns:
            bool: 是否加载成功
        """
        # 处理lang_codes参数
        if isinstance(lang_codes, str):
            if lang_codes == "multi":
                target_languages = self.supported_languages
                logger.info("多语种模式：预加载所有支持的语种模型")
            else:
                target_languages = [lang_codes]
                logger.info(f"单语种模式：加载语种 {lang_codes}")
        else:
            target_languages = lang_codes
            logger.info(f"指定语种模式：加载语种 {target_languages}")

        # 加载每个语种的模型
        success_count = 0
        for lang_code in target_languages:
            if self._load_single_language(lang_code):
                success_count += 1
            else:
                logger.warning(f"加载语种 {lang_code} 失败")

        logger.info(f"模型加载完成：成功 {success_count}/{len(target_languages)} 个语种")
        return success_count > 0

    def _load_single_language(self, lang_code: str) -> bool:
        """
        加载指定语种的ASR模型

        Args:
            lang_code: 语种代码

        Returns:
            bool: 是否加载成功
        """
        with self.model_lock:
            if lang_code in self.loaded_models:
                logger.info(f"语种 {lang_code} 的模型已加载")
                return True

            try:
                # 获取语种配置
                lang_config = config_manager.load_language_config(lang_code)
                if not lang_config:
                    logger.error(f"无法加载语种 {lang_code} 的配置")
                    return False

                # 验证模型路径
                if not os.path.exists(lang_config.onnx_dir):
                    logger.error(f"语种 {lang_code} 的模型路径不存在: {lang_config.onnx_dir}")
                    return False

                # 保存当前全局ONNX_SESSIONS
                original_sessions = ONNX_SESSIONS.copy()
                ONNX_SESSIONS.clear()

                # 加载语种特定的模型
                metadatas = load_onnx(
                    lang_config.onnx_dir,
                    lang_config.fp16,
                    lang_config.quant,
                    self.global_config.decode.mode,
                    lang_config.device,
                    lang_config.device_id
                )

                # 使用新的配置构建方法
                model_config = lang_config.build_model_config(metadatas, self.global_config.decode)

                # 创建词表
                symbol_table = SymbolTable(
                    lang_config.dict_path,
                    lang_code,
                    lang_config.map_path,
                    lang_config.lower,
                    lang_config.remove_spm
                )

                # 创建特征管道
                from modules.feature import FeaturePipeline
                feat_pipe = FeaturePipeline(model_config['feat_configs'])

                # 保存加载的模型信息（不再存储sessions）
                self.loaded_models[lang_code] = {
                    'metadatas': metadatas,
                    'config': model_config,
                    'symbol_table': symbol_table,
                    'lang_config': lang_config,
                    'feat_pipe': feat_pipe
                }

                # 立即注册模型到会话池（如果启用）
                if self.session_pool and self.session_pool.enabled:
                    self._register_models_to_session_pool(lang_code, lang_config, ONNX_SESSIONS)
                    logger.info(f"语种 {lang_code} 的模型已注册到会话池")

                # 恢复原始ONNX_SESSIONS
                ONNX_SESSIONS.clear()
                ONNX_SESSIONS.update(original_sessions)

                logger.info(f"成功加载语种 {lang_code} 的ASR模型")
                return True

            except Exception as e:
                logger.error(f"加载语种 {lang_code} 的ASR模型失败: {e}")
                return False

    def _register_models_to_session_pool(self, lang_code: str, lang_config, current_sessions):
        """
        将模型注册到会话池

        Args:
            lang_code: 语种代码
            lang_config: 语种配置
            current_sessions: 当前加载的ONNX会话字典
        """
        try:
            # 为每个模型类型注册会话工厂
            for model_type in ['encoder', 'ctc', 'decoder']:
                if model_type in current_sessions:
                    model_name = f"{lang_code}_{model_type}"

                    # 创建会话工厂函数
                    def create_session_factory(model_type=model_type, lang_config=lang_config):
                        def factory():
                            return self._create_onnx_session(model_type, lang_config)
                        return factory

                    # 注册到会话池
                    self.session_pool.register_model(
                        model_name,
                        create_session_factory(model_type, lang_config)
                    )

                    logger.debug(f"已将模型 {model_name} 注册到会话池")

        except Exception as e:
            logger.error(f"注册模型到会话池失败: {e}")

    def _create_onnx_session(self, model_type: str, lang_config):
        """
        创建ONNX会话的工厂函数

        Args:
            model_type: 模型类型 (encoder/ctc/decoder)
            lang_config: 语种配置

        Returns:
            tuple: (session, input_names)
        """
        import onnx
        from modules.decoder import get_ep_list

        # 构建模型路径
        model_path = os.path.join(lang_config.onnx_dir, f"{model_type}.onnx")
        if not os.path.exists(model_path):
            raise FileNotFoundError(f"模型文件不存在: {model_path}")

        # 获取执行提供者
        EP_list = get_ep_list(lang_config.device, lang_config.device_id)

        # 加载模型获取输入名称
        model = onnx.load(model_path)
        input_names = [node.name for node in model.graph.input]

        # 创建会话
        session = ort.InferenceSession(model_path, providers=EP_list)

        return session, input_names

    def switch_to_language(self, lang_code: str) -> bool:
        """
        切换到指定语种的模型

        Args:
            lang_code: 目标语种代码

        Returns:
            bool: 是否切换成功
        """
        if lang_code not in self.supported_languages:
            logger.warning(f"不支持的语种: {lang_code}")
            return False

        # 如果模型未加载, 先加载
        if lang_code not in self.loaded_models:
            if not self._load_single_language(lang_code):
                return False

        try:
            # 由于使用会话池，不再需要切换全局ONNX_SESSIONS
            # 只需要确认模型已加载即可
            logger.info(f"语种 {lang_code} 已准备就绪（使用会话池）")
            return True

        except Exception as e:
            logger.error(f"切换到语种 {lang_code} 失败: {e}")
            return False

    def get_symbol_table(self, lang_code: str) -> Optional[SymbolTable]:
        """获取指定语种的词表"""
        if lang_code in self.loaded_models:
            return self.loaded_models[lang_code]['symbol_table']
        return None

    def get_config(self, lang_code: str) -> Optional[Dict]:
        """获取指定语种的配置"""
        if lang_code in self.loaded_models:
            return self.loaded_models[lang_code]['config']
        return None

    def get_lang_config(self, lang_code: str) -> Optional[Any]:
        """获取指定语种的语言配置"""
        if lang_code in self.loaded_models:
            return self.loaded_models[lang_code]['lang_config']
        return None

    def get_feat_pipe(self, lang_code: str) -> Optional[Any]:
        """获取指定语种的特征管道"""
        if lang_code in self.loaded_models:
            return self.loaded_models[lang_code]['feat_pipe']
        return None
    
    def preload_common_languages(self, languages: List[str] = None):
        """
        预加载常用语种的模型

        Args:
            languages: 要预加载的语种列表, 默认为所有支持的语种
        """
        if languages is None:
            # 默认预加载所有支持的语种以确保实时切换
            languages = self.supported_languages.copy()

        logger.info(f"开始预加载语种: {languages}")
        return self.load_models(languages)
    
    def is_language_loaded(self, lang_code: str) -> bool:
        """检查指定语种是否已加载"""
        return lang_code in self.loaded_models
    
    def get_loaded_languages(self) -> List[str]:
        """获取已加载的语种列表"""
        return list(self.loaded_models.keys())

    def unload_language(self, lang_code: str):
        """卸载指定语种的模型"""
        if lang_code in self.loaded_models:
            del self.loaded_models[lang_code]
            logger.info(f"已卸载语种 {lang_code} 的模型")

    def cleanup(self):
        """清理所有资源"""
        logger.info("清理ASR管理器资源")
        self.loaded_models.clear()
        # 不再需要清理ONNX_SESSIONS，因为我们使用会话池


