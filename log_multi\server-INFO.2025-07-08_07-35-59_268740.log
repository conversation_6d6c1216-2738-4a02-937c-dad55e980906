2025-07-08 07:35:59.275 | INFO  | modules.config:init_logger :594 - 日志系统初始化成功, 配置: {'level': 'INFO', 'rotation': '1 day', 'retention': '7 days', 'compression': 'zip', 'max_file_size': '100 MB'}
2025-07-08 07:35:59.305 | INFO  | modules.monitoring:_start_health_server:301 - 健康检查服务器已启动, 端口: 8081
2025-07-08 07:35:59.307 | INFO  | modules.monitoring:__init__    :174 - 系统监控已启用, 内存检查间隔: 60秒
2025-07-08 07:35:59.307 | INFO  | modules.config:init_monitoring:625 - 监控系统初始化成功
2025-07-08 07:35:59.311 | INFO  | modules.onnx_session_pool:__init__    :58 - ONNX会话池已启用, 每个模型最大会话数: 4
2025-07-08 07:35:59.391 | INFO  | modules.config:init_session_pool:651 - ONNX会话池初始化成功
2025-07-08 07:35:59.392 | INFO  | modules.config:init_all_modules:669 - 所有模块初始化完成
2025-07-08 07:35:59.396 | INFO  | modules.asr_manager:__init__    :36 - 初始化统一ASR管理器, 支持语种: ['zh', 'en', 'ru', 'kk', 'kkin', 'ug']
2025-07-08 07:35:59.398 | INFO  | server :lifespan    :83 - 启动多语种ASR服务模式
2025-07-08 07:35:59.399 | INFO  | server :lifespan    :84 - 多语种模式：预加载所有支持的语种模型以确保实时切换性能
2025-07-08 07:35:59.400 | INFO  | modules.asr_manager:load_models :53 - 多语种模式：预加载所有支持的语种模型
2025-07-08 07:35:59.401 | INFO  | modules.decoder:load_onnx   :61 - 加载模型: /ws/MODELS/online_onnx_zh, fp16: False, quant: False, device: cpu
2025-07-08 07:36:00.959 | INFO  | modules.symbol_table:load_dict   :28 - 加载词表: /ws/MODELS/online_onnx_zh/units.txt
2025-07-08 07:36:00.966 | INFO  | modules.symbol_table:__init__    :22 - 启用后处理: 转全小写字母
2025-07-08 07:36:00.966 | INFO  | modules.asr_manager:_load_single_language:143 - 成功加载语种 zh 的ASR模型
2025-07-08 07:36:00.967 | INFO  | modules.decoder:load_onnx   :61 - 加载模型: /ws/MODELS/online_onnx_en, fp16: False, quant: False, device: cpu
2025-07-08 07:36:03.018 | INFO  | modules.symbol_table:load_dict   :28 - 加载词表: /ws/MODELS/online_onnx_en/units.txt
2025-07-08 07:36:03.026 | INFO  | modules.symbol_table:__init__    :22 - 启用后处理: 转全小写字母
2025-07-08 07:36:03.026 | INFO  | modules.symbol_table:__init__    :25 - 启用后处理: 去占位下划线
2025-07-08 07:36:03.027 | INFO  | modules.asr_manager:_load_single_language:143 - 成功加载语种 en 的ASR模型
2025-07-08 07:36:03.027 | INFO  | modules.decoder:load_onnx   :61 - 加载模型: /ws/MODELS/online_onnx_ru, fp16: False, quant: True, device: cpu
2025-07-08 07:36:03.547 | INFO  | modules.symbol_table:load_dict   :28 - 加载词表: /ws/MODELS/online_onnx_ru/units.txt
2025-07-08 07:36:03.556 | INFO  | modules.symbol_table:__init__    :22 - 启用后处理: 转全小写字母
2025-07-08 07:36:03.556 | INFO  | modules.symbol_table:__init__    :25 - 启用后处理: 去占位下划线
2025-07-08 07:36:03.557 | INFO  | modules.asr_manager:_load_single_language:143 - 成功加载语种 ru 的ASR模型
2025-07-08 07:36:03.557 | INFO  | modules.decoder:load_onnx   :61 - 加载模型: /ws/MODELS/online_onnx_kk, fp16: False, quant: True, device: cpu
2025-07-08 07:36:04.346 | INFO  | modules.symbol_table:load_dict   :28 - 加载词表: /ws/MODELS/online_onnx_kk/units.txt
2025-07-08 07:36:04.355 | INFO  | modules.symbol_table:__init__    :22 - 启用后处理: 转全小写字母
2025-07-08 07:36:04.356 | INFO  | modules.symbol_table:__init__    :25 - 启用后处理: 去占位下划线
2025-07-08 07:36:04.356 | INFO  | modules.asr_manager:_load_single_language:143 - 成功加载语种 kk 的ASR模型
2025-07-08 07:36:04.356 | INFO  | modules.decoder:load_onnx   :61 - 加载模型: /ws/MODELS/online_onnx_kkin, fp16: False, quant: True, device: cpu
2025-07-08 07:36:04.832 | INFO  | modules.symbol_table:load_dict   :28 - 加载词表: /ws/MODELS/online_onnx_kkin/units.txt
2025-07-08 07:36:04.843 | INFO  | modules.symbol_table:__init__    :13 - 加载映射表: /ws/MODELS/online_onnx_kkin/map_kkin2lat.txt
2025-07-08 07:36:04.844 | INFO  | modules.asr_manager:_load_single_language:143 - 成功加载语种 kkin 的ASR模型
2025-07-08 07:36:04.844 | INFO  | modules.decoder:load_onnx   :61 - 加载模型: /ws/MODELS/online_onnx_ug, fp16: False, quant: True, device: cpu
2025-07-08 07:36:05.324 | INFO  | modules.symbol_table:load_dict   :28 - 加载词表: /ws/MODELS/online_onnx_ug/units.txt
2025-07-08 07:36:05.334 | INFO  | modules.symbol_table:__init__    :13 - 加载映射表: /ws/MODELS/online_onnx_ug/map_uyg2lat.txt
2025-07-08 07:36:05.334 | INFO  | modules.asr_manager:_load_single_language:143 - 成功加载语种 ug 的ASR模型
2025-07-08 07:36:05.335 | INFO  | modules.asr_manager:load_models :69 - 模型加载完成：成功 6/6 个语种
2025-07-08 07:36:05.335 | INFO  | server :lifespan    :90 - 多语种模式启动成功，所有语种模型已预加载
2025-07-08 07:36:05.417 | INFO  | modules.lid_manager:_load_lid_model:95 - LID模型加载成功: /ws/MODELS/lid_model/lid.onnx
2025-07-08 07:36:05.418 | INFO  | modules.lid_manager:_load_lid_model:105 - LID语种字典加载成功: /ws/MODELS/lid_model/spk2id.json
2025-07-08 07:36:05.419 | INFO  | modules.lid_manager:_load_lid_model:114 - 应用全局CMVN: /ws/MODELS/lid_model/global_cmvn
2025-07-08 07:36:05.419 | INFO  | modules.lid_manager:__init__    :71 - LID管理器初始化完成
2025-07-08 07:36:05.419 | INFO  | server :lifespan    :106 - LID管理器初始化成功: /ws/MODELS/lid_model/lid.onnx
2025-07-08 07:36:05.419 | INFO  | server :lifespan    :118 - Server start, init manager, LID_MANAGER, ASR_MANAGER

2025-07-08 07:42:48.169 | INFO  | server :websocket_endpoint:233 - 新建客户连接 "client_id": "111"

2025-07-08 07:42:48.171 | INFO  | server :websocket_endpoint:233 - 新建客户连接 "client_id": "222"

2025-07-08 07:42:48.172 | INFO  | server :websocket_endpoint:233 - 新建客户连接 "client_id": "666"

2025-07-08 07:42:48.173 | INFO  | server :websocket_endpoint:233 - 新建客户连接 "client_id": "333"

2025-07-08 07:42:48.178 | INFO  | server :websocket_endpoint:233 - 新建客户连接 "client_id": "444"

2025-07-08 07:42:48.179 | INFO  | server :websocket_endpoint:233 - 新建客户连接 "client_id": "555"

2025-07-08 07:42:48.180 | INFO  | server :websocket_endpoint:233 - 新建客户连接 "client_id": "777"

2025-07-08 07:42:48.362 | INFO  | modules.connect:on_check    :403 - client_id:111 - 设置自定义分隔符: ", "
2025-07-08 07:42:48.370 | INFO  | modules.connect:_process_silence_and_lid:664 - client_id:111 - 片段0 检测到有效语音, 开始语种识别
2025-07-08 07:42:48.431 | INFO  | modules.connect:_perform_lid:724 - client_id:111 - LID尝试 1: 语种=zh, {'predict': 'zh', 'scores': {'ar': 0.63, 'en': 0.0, 'hin': 0.61, 'id': 0.45, 'ja': 0.89, 'kk': 0.52, 'kkin': 0.67, 'ko': 0.59, 'ms': 0.26, 'my': 0.32, 'ru': 0.58, 'th': 0.54, 'ug': 0.83, 'vi': 0.41, 'zh': 1.0}, 'confidence': 0.11, 'duration': 0.38}
2025-07-08 07:42:48.532 | INFO  | modules.connect:on_check    :403 - client_id:222 - 设置自定义分隔符: ", "
2025-07-08 07:42:48.540 | INFO  | modules.connect:_process_silence_and_lid:664 - client_id:222 - 片段0 检测到有效语音, 开始语种识别
2025-07-08 07:42:48.592 | INFO  | modules.connect:_perform_lid:724 - client_id:222 - LID尝试 1: 语种=kkin, {'predict': 'kkin', 'scores': {'ar': 0.44, 'en': 0.0, 'hin': 0.4, 'id': 0.12, 'ja': 0.72, 'kk': 0.51, 'kkin': 1.0, 'ko': 0.34, 'ms': 0.06, 'my': 0.21, 'ru': 0.46, 'th': 0.52, 'ug': 0.88, 'vi': 0.33, 'zh': 0.65}, 'confidence': 0.12, 'duration': 0.26}
2025-07-08 07:42:48.703 | INFO  | modules.connect:on_check    :403 - client_id:666 - 设置自定义分隔符: ", "
2025-07-08 07:42:48.711 | INFO  | modules.connect:_process_silence_and_lid:664 - client_id:666 - 片段0 检测到有效语音, 开始语种识别
2025-07-08 07:42:48.779 | INFO  | modules.connect:_perform_lid:724 - client_id:666 - LID尝试 1: 语种=ja, {'predict': 'ja', 'scores': {'ar': 0.65, 'en': 0.0, 'hin': 0.41, 'id': 0.15, 'ja': 1.0, 'kk': 0.34, 'kkin': 0.78, 'ko': 0.42, 'ms': 0.23, 'my': 0.24, 'ru': 0.59, 'th': 0.59, 'ug': 0.6, 'vi': 0.38, 'zh': 0.66}, 'confidence': 0.22, 'duration': 0.22}
2025-07-08 07:42:48.866 | INFO  | modules.connect:on_check    :403 - client_id:333 - 设置自定义分隔符: ", "
2025-07-08 07:42:48.874 | INFO  | modules.connect:_process_silence_and_lid:664 - client_id:333 - 片段0 检测到有效语音, 开始语种识别
2025-07-08 07:42:48.955 | INFO  | modules.connect:_perform_lid:724 - client_id:333 - LID尝试 1: 语种=zh, {'predict': 'zh', 'scores': {'ar': 0.58, 'en': 0.0, 'hin': 0.79, 'id': 0.14, 'ja': 0.76, 'kk': 0.67, 'kkin': 0.75, 'ko': 0.8, 'ms': 0.14, 'my': 0.35, 'ru': 0.51, 'th': 0.54, 'ug': 0.76, 'vi': 0.25, 'zh': 1.0}, 'confidence': 0.2, 'duration': 0.36}
2025-07-08 07:42:49.012 | INFO  | modules.connect:on_check    :403 - client_id:444 - 设置自定义分隔符: ", "
2025-07-08 07:42:49.020 | INFO  | modules.connect:_process_silence_and_lid:664 - client_id:444 - 片段0 检测到有效语音, 开始语种识别
2025-07-08 07:42:49.105 | INFO  | modules.connect:_perform_lid:724 - client_id:444 - LID尝试 1: 语种=ja, {'predict': 'ja', 'scores': {'ar': 0.4, 'en': 0.0, 'hin': 0.37, 'id': 0.0, 'ja': 1.0, 'kk': 0.58, 'kkin': 0.41, 'ko': 0.48, 'ms': 0.17, 'my': 0.29, 'ru': 0.43, 'th': 0.43, 'ug': 0.68, 'vi': 0.31, 'zh': 0.74}, 'confidence': 0.26, 'duration': 0.38}
2025-07-08 07:42:49.197 | INFO  | modules.connect:on_check    :403 - client_id:555 - 设置自定义分隔符: ", "
2025-07-08 07:42:49.364 | INFO  | modules.connect:on_check    :403 - client_id:777 - 设置自定义分隔符: ", "
2025-07-08 07:42:49.371 | INFO  | modules.connect:_process_silence_and_lid:664 - client_id:777 - 片段0 检测到有效语音, 开始语种识别
2025-07-08 07:42:49.422 | INFO  | modules.connect:_perform_lid:724 - client_id:777 - LID尝试 1: 语种=ja, {'predict': 'ja', 'scores': {'ar': 0.36, 'en': 0.0, 'hin': 0.29, 'id': 0.04, 'ja': 1.0, 'kk': 0.33, 'kkin': 0.3, 'ko': 0.34, 'ms': 0.09, 'my': 0.2, 'ru': 0.33, 'th': 0.34, 'ug': 0.41, 'vi': 0.25, 'zh': 0.59}, 'confidence': 0.41, 'duration': 0.38}
2025-07-08 07:42:49.484 | INFO  | modules.connect:_perform_lid:724 - client_id:111 - LID尝试 2: 语种=ar, {'predict': 'ar', 'scores': {'ar': 1.0, 'en': 0.92, 'hin': 0.39, 'id': 0.0, 'ja': 0.76, 'kk': 0.47, 'kkin': 0.38, 'ko': 0.6, 'ms': 0.24, 'my': 0.44, 'ru': 0.93, 'th': 0.81, 'ug': 0.46, 'vi': 0.28, 'zh': 0.8}, 'confidence': 0.07, 'duration': 0.78}
2025-07-08 07:42:49.533 | INFO  | modules.connect:_perform_lid:724 - client_id:222 - LID尝试 2: 语种=ja, {'predict': 'ja', 'scores': {'ar': 0.57, 'en': 0.07, 'hin': 0.43, 'id': 0.0, 'ja': 1.0, 'kk': 0.66, 'kkin': 0.68, 'ko': 0.5, 'ms': 0.0, 'my': 0.24, 'ru': 0.54, 'th': 0.34, 'ug': 0.67, 'vi': 0.26, 'zh': 0.63}, 'confidence': 0.32, 'duration': 0.66}
2025-07-08 07:42:49.582 | INFO  | modules.connect:_perform_lid:724 - client_id:666 - LID尝试 2: 语种=ug, {'predict': 'ug', 'scores': {'ar': 0.38, 'en': 0.0, 'hin': 0.48, 'id': 0.3, 'ja': 0.68, 'kk': 0.44, 'kkin': 0.94, 'ko': 0.47, 'ms': 0.11, 'my': 0.3, 'ru': 0.43, 'th': 0.64, 'ug': 1.0, 'vi': 0.31, 'zh': 0.67}, 'confidence': 0.06, 'duration': 0.62}
2025-07-08 07:42:49.638 | INFO  | modules.connect:_perform_lid:724 - client_id:333 - LID尝试 2: 语种=ja, {'predict': 'ja', 'scores': {'ar': 0.45, 'en': 0.04, 'hin': 0.31, 'id': 0.11, 'ja': 1.0, 'kk': 0.49, 'kkin': 0.55, 'ko': 0.37, 'ms': 0.0, 'my': 0.14, 'ru': 0.44, 'th': 0.39, 'ug': 0.52, 'vi': 0.2, 'zh': 0.55}, 'confidence': 0.45, 'duration': 0.76}
2025-07-08 07:42:49.694 | INFO  | modules.connect:_perform_lid:724 - client_id:444 - LID尝试 2: 语种=ja, {'predict': 'ja', 'scores': {'ar': 0.58, 'en': 0.0, 'hin': 0.26, 'id': 0.04, 'ja': 1.0, 'kk': 0.7, 'kkin': 0.71, 'ko': 0.52, 'ms': 0.0, 'my': 0.23, 'ru': 0.52, 'th': 0.45, 'ug': 0.98, 'vi': 0.23, 'zh': 0.64}, 'confidence': 0.02, 'duration': 0.78}
2025-07-08 07:42:49.700 | INFO  | modules.connect:_process_silence_and_lid:664 - client_id:555 - 片段0 检测到有效语音, 开始语种识别
2025-07-08 07:42:49.739 | INFO  | modules.connect:_perform_lid:724 - client_id:555 - LID尝试 1: 语种=kkin, {'predict': 'kkin', 'scores': {'ar': 0.51, 'en': 0.17, 'hin': 0.3, 'id': 0.0, 'ja': 0.54, 'kk': 0.33, 'kkin': 1.0, 'ko': 0.41, 'ms': 0.15, 'my': 0.21, 'ru': 0.5, 'th': 0.4, 'ug': 0.42, 'vi': 0.17, 'zh': 0.84}, 'confidence': 0.16, 'duration': 0.46}
2025-07-08 07:42:49.832 | INFO  | modules.connect:_perform_lid:724 - client_id:777 - LID尝试 2: 语种=ja, {'predict': 'ja', 'scores': {'ar': 0.34, 'en': 0.07, 'hin': 0.2, 'id': 0.07, 'ja': 1.0, 'kk': 0.48, 'kkin': 0.52, 'ko': 0.38, 'ms': 0.0, 'my': 0.12, 'ru': 0.31, 'th': 0.34, 'ug': 0.51, 'vi': 0.23, 'zh': 0.52}, 'confidence': 0.48, 'duration': 0.78}
2025-07-08 07:42:50.091 | INFO  | modules.connect:_perform_lid:724 - client_id:555 - LID尝试 2: 语种=hin, {'predict': 'hin', 'scores': {'ar': 0.39, 'en': 0.0, 'hin': 1.0, 'id': 0.08, 'ja': 0.48, 'kk': 0.22, 'kkin': 0.89, 'ko': 0.35, 'ms': 0.11, 'my': 0.21, 'ru': 0.39, 'th': 0.3, 'ug': 0.33, 'vi': 0.15, 'zh': 0.46}, 'confidence': 0.11, 'duration': 0.86}
2025-07-08 07:42:50.314 | INFO  | modules.connect:_perform_lid:724 - client_id:111 - LID尝试 3: 语种=ar, {'predict': 'ar', 'scores': {'ar': 1.0, 'en': 0.61, 'hin': 0.21, 'id': 0.48, 'ja': 0.5, 'kk': 0.27, 'kkin': 0.38, 'ko': 0.7, 'ms': 0.08, 'my': 0.2, 'ru': 0.67, 'th': 0.33, 'ug': 0.26, 'vi': 0.0, 'zh': 0.48}, 'confidence': 0.3, 'duration': 1.58}
2025-07-08 07:42:50.420 | INFO  | modules.connect:_perform_lid:724 - client_id:222 - LID尝试 3: 语种=ug, {'predict': 'ug', 'scores': {'ar': 0.34, 'en': 0.41, 'hin': 0.15, 'id': 0.06, 'ja': 0.27, 'kk': 0.24, 'kkin': 0.52, 'ko': 0.34, 'ms': 0.0, 'my': 0.18, 'ru': 0.32, 'th': 0.19, 'ug': 1.0, 'vi': 0.17, 'zh': 0.24}, 'confidence': 0.48, 'duration': 1.46}
2025-07-08 07:42:50.520 | INFO  | modules.connect:_perform_lid:724 - client_id:666 - LID尝试 3: 语种=en, {'predict': 'en', 'scores': {'ar': 0.36, 'en': 1.0, 'hin': 0.38, 'id': 0.0, 'ja': 0.62, 'kk': 0.28, 'kkin': 0.68, 'ko': 0.57, 'ms': 0.02, 'my': 0.23, 'ru': 0.66, 'th': 0.3, 'ug': 0.48, 'vi': 0.25, 'zh': 0.42}, 'confidence': 0.32, 'duration': 1.42}
2025-07-08 07:42:50.629 | INFO  | modules.connect:_perform_lid:724 - client_id:333 - LID尝试 3: 语种=ug, {'predict': 'ug', 'scores': {'ar': 0.62, 'en': 0.39, 'hin': 0.1, 'id': 0.15, 'ja': 0.97, 'kk': 0.4, 'kkin': 0.53, 'ko': 0.13, 'ms': 0.0, 'my': 0.36, 'ru': 0.42, 'th': 0.2, 'ug': 1.0, 'vi': 0.25, 'zh': 0.12}, 'confidence': 0.03, 'duration': 1.56}
2025-07-08 07:42:50.963 | INFO  | modules.connect:_perform_lid:724 - client_id:555 - LID尝试 3: 语种=kkin, {'predict': 'kkin', 'scores': {'ar': 0.63, 'en': 0.0, 'hin': 0.69, 'id': 0.03, 'ja': 0.52, 'kk': 0.23, 'kkin': 1.0, 'ko': 0.44, 'ms': 0.24, 'my': 0.31, 'ru': 0.52, 'th': 0.36, 'ug': 0.45, 'vi': 0.21, 'zh': 0.59}, 'confidence': 0.31, 'duration': 1.66}
2025-07-08 07:42:51.620 | INFO  | modules.connect:_perform_lid:724 - client_id:222 - LID尝试 4: 语种=en, {'predict': 'en', 'scores': {'ar': 0.22, 'en': 1.0, 'hin': 0.09, 'id': 0.09, 'ja': 0.28, 'kk': 0.42, 'kkin': 0.68, 'ko': 0.25, 'ms': 0.0, 'my': 0.31, 'ru': 0.23, 'th': 0.09, 'ug': 0.57, 'vi': 0.28, 'zh': 0.13}, 'confidence': 0.32, 'duration': 2.66}
2025-07-08 07:42:51.817 | INFO  | modules.connect:_perform_lid:724 - client_id:333 - LID尝试 4: 语种=en, {'predict': 'en', 'scores': {'ar': 0.0, 'en': 1.0, 'hin': 0.09, 'id': 0.16, 'ja': 0.52, 'kk': 0.41, 'kkin': 0.17, 'ko': 0.1, 'ms': 0.07, 'my': 0.31, 'ru': 0.15, 'th': 0.06, 'ug': 0.24, 'vi': 0.15, 'zh': 0.27}, 'confidence': 0.48, 'duration': 2.76}
2025-07-08 07:42:51.938 | INFO  | modules.connect:_perform_lid:724 - client_id:777 - LID尝试 3: 语种=zh, {'predict': 'zh', 'scores': {'ar': 0.62, 'en': 0.09, 'hin': 0.68, 'id': 0.0, 'ja': 0.8, 'kk': 0.23, 'kkin': 0.53, 'ko': 0.56, 'ms': 0.02, 'my': 0.3, 'ru': 0.47, 'th': 0.42, 'ug': 0.49, 'vi': 0.37, 'zh': 1.0}, 'confidence': 0.2, 'duration': 1.62}
2025-07-08 07:42:52.162 | INFO  | modules.connect:_perform_lid:724 - client_id:444 - LID尝试 3: 语种=en, {'predict': 'en', 'scores': {'ar': 0.17, 'en': 1.0, 'hin': 0.0, 'id': 0.29, 'ja': 0.58, 'kk': 0.42, 'kkin': 0.3, 'ko': 0.09, 'ms': 0.16, 'my': 0.34, 'ru': 0.17, 'th': 0.17, 'ug': 0.39, 'vi': 0.36, 'zh': 0.07}, 'confidence': 0.42, 'duration': 3.18}
2025-07-08 07:42:52.364 | INFO  | modules.connect:_perform_lid:724 - client_id:555 - LID尝试 4: 语种=kkin, {'predict': 'kkin', 'scores': {'ar': 0.31, 'en': 0.6, 'hin': 0.9, 'id': 0.03, 'ja': 0.59, 'kk': 0.46, 'kkin': 1.0, 'ko': 0.46, 'ms': 0.26, 'my': 0.13, 'ru': 0.63, 'th': 0.0, 'ug': 0.82, 'vi': 0.29, 'zh': 0.48}, 'confidence': 0.1, 'duration': 2.86}
2025-07-08 07:42:52.619 | INFO  | modules.connect:_perform_lid:724 - client_id:333 - LID尝试 5: 语种=en, {'predict': 'en', 'scores': {'ar': 0.0, 'en': 1.0, 'hin': 0.02, 'id': 0.13, 'ja': 0.42, 'kk': 0.36, 'kkin': 0.12, 'ko': 0.04, 'ms': 0.05, 'my': 0.26, 'ru': 0.12, 'th': 0.05, 'ug': 0.17, 'vi': 0.19, 'zh': 0.16}, 'confidence': 0.58, 'duration': 3.56}
2025-07-08 07:42:53.144 | INFO  | modules.connect:_perform_lid:724 - client_id:555 - LID尝试 5: 语种=kkin, {'predict': 'kkin', 'scores': {'ar': 0.25, 'en': 0.41, 'hin': 0.69, 'id': 0.34, 'ja': 0.57, 'kk': 0.56, 'kkin': 1.0, 'ko': 0.4, 'ms': 0.41, 'my': 0.22, 'ru': 0.31, 'th': 0.11, 'ug': 0.61, 'vi': 0.24, 'zh': 0.0}, 'confidence': 0.31, 'duration': 3.66}
2025-07-08 07:42:53.371 | INFO  | modules.connect:_perform_lid:724 - client_id:777 - LID尝试 4: 语种=en, {'predict': 'en', 'scores': {'ar': 0.34, 'en': 1.0, 'hin': 0.23, 'id': 0.09, 'ja': 0.87, 'kk': 0.45, 'kkin': 0.37, 'ko': 0.35, 'ms': 0.0, 'my': 0.23, 'ru': 0.29, 'th': 0.14, 'ug': 0.22, 'vi': 0.2, 'zh': 0.56}, 'confidence': 0.13, 'duration': 2.82}
2025-07-08 07:42:53.678 | INFO  | modules.connect:_perform_lid:724 - client_id:333 - LID尝试 6: 语种=en, {'predict': 'en', 'scores': {'ar': 0.0, 'en': 1.0, 'hin': 0.07, 'id': 0.17, 'ja': 0.33, 'kk': 0.33, 'kkin': 0.14, 'ko': 0.11, 'ms': 0.13, 'my': 0.3, 'ru': 0.14, 'th': 0.12, 'ug': 0.18, 'vi': 0.16, 'zh': 0.13}, 'confidence': 0.67, 'duration': 4.36}
2025-07-08 07:42:53.678 | INFO  | modules.connect:_perform_lid:741 - client_id:333 - LID结果确认: 语种=en, 置信度=0.67
2025-07-08 07:42:53.679 | INFO  | modules.asr_manager:switch_to_language:176 - 成功切换到语种: en
2025-07-08 07:42:53.679 | INFO  | modules.connect:_switch_asr_model:799 - client_id:333 - 成功切换到语种: en
2025-07-08 07:42:53.687 | INFO  | modules.connect:on_check    :557 - client_id:333 - >>> [解析] 第10个数据包, 累计帧数: 438
2025-07-08 07:42:53.687 | INFO  | modules.connect:_init_decoder:121 - client_id:333 - 初始化解码器, 使用语种: en
2025-07-08 07:42:53.689 | INFO  | modules.connect:_init_decoder:135 - client_id:333 - 解码器初始化完成, 分隔符: ", "
2025-07-08 07:42:53.690 | INFO  | modules.connect:on_decode   :839 - client_id:333 - 延迟初始化解码器完成
2025-07-08 07:42:56.302 | INFO  | modules.connect:on_result   :366 - client_id:333 - <<< [发送] 第0个数据包, 更新识别结果: "the city is also the base to climb"
2025-07-08 07:42:56.654 | INFO  | modules.connect:_perform_lid:724 - client_id:444 - LID尝试 4: 语种=en, {'predict': 'en', 'scores': {'ar': 0.08, 'en': 1.0, 'hin': 0.0, 'id': 0.22, 'ja': 0.47, 'kk': 0.39, 'kkin': 0.27, 'ko': 0.23, 'ms': 0.06, 'my': 0.33, 'ru': 0.1, 'th': 0.13, 'ug': 0.3, 'vi': 0.34, 'zh': 0.0}, 'confidence': 0.53, 'duration': 4.38}
2025-07-08 07:42:56.686 | INFO  | modules.connect:on_check    :557 - client_id:333 - >>> [解析] 第11个数据包, 累计帧数: 476
2025-07-08 07:42:56.692 | INFO  | modules.connect:on_check    :557 - client_id:333 - >>> [解析] 第12个数据包, 累计帧数: 514
2025-07-08 07:42:57.231 | INFO  | modules.connect:on_result   :366 - client_id:333 - <<< [发送] 第1个数据包, 更新识别结果: "the city is also the base to climb the nira"
2025-07-08 07:42:57.502 | INFO  | modules.connect:_perform_lid:724 - client_id:777 - LID尝试 5: 语种=en, {'predict': 'en', 'scores': {'ar': 0.39, 'en': 1.0, 'hin': 0.3, 'id': 0.11, 'ja': 0.8, 'kk': 0.51, 'kkin': 0.38, 'ko': 0.37, 'ms': 0.0, 'my': 0.29, 'ru': 0.25, 'th': 0.12, 'ug': 0.43, 'vi': 0.2, 'zh': 0.46}, 'confidence': 0.2, 'duration': 3.62}
2025-07-08 07:42:57.509 | INFO  | modules.connect:on_check    :557 - client_id:333 - >>> [解析] 第13个数据包, 累计帧数: 552
2025-07-08 07:42:57.855 | INFO  | modules.connect:_perform_lid:724 - client_id:111 - LID尝试 4: 语种=en, {'predict': 'en', 'scores': {'ar': 0.0, 'en': 1.0, 'hin': 0.06, 'id': 0.14, 'ja': 0.28, 'kk': 0.21, 'kkin': 0.08, 'ko': 0.06, 'ms': 0.11, 'my': 0.22, 'ru': 0.08, 'th': 0.05, 'ug': 0.04, 'vi': 0.15, 'zh': 0.14}, 'confidence': 0.72, 'duration': 5.18}
2025-07-08 07:42:57.856 | INFO  | modules.connect:_perform_lid:741 - client_id:111 - LID结果确认: 语种=en, 置信度=0.72
2025-07-08 07:42:57.856 | INFO  | modules.asr_manager:switch_to_language:176 - 成功切换到语种: en
2025-07-08 07:42:57.856 | INFO  | modules.connect:_switch_asr_model:799 - client_id:111 - 成功切换到语种: en
2025-07-08 07:42:57.872 | INFO  | modules.connect:on_check    :557 - client_id:111 - >>> [解析] 第12个数据包, 累计帧数: 518
2025-07-08 07:42:57.873 | INFO  | modules.connect:_init_decoder:121 - client_id:111 - 初始化解码器, 使用语种: en
2025-07-08 07:42:57.876 | INFO  | modules.connect:_init_decoder:135 - client_id:111 - 解码器初始化完成, 分隔符: ", "
2025-07-08 07:42:57.876 | INFO  | modules.connect:on_decode   :839 - client_id:111 - 延迟初始化解码器完成
2025-07-08 07:43:00.389 | INFO  | modules.connect:on_result   :366 - client_id:111 - <<< [发送] 第0个数据包, 更新识别结果: "the surface of the moon is made of rocks and dusts the outer layer of the moon is called"
2025-07-08 07:43:00.741 | INFO  | modules.connect:_perform_lid:724 - client_id:444 - LID尝试 5: 语种=en, {'predict': 'en', 'scores': {'ar': 0.04, 'en': 1.0, 'hin': 0.02, 'id': 0.26, 'ja': 0.45, 'kk': 0.35, 'kkin': 0.25, 'ko': 0.14, 'ms': 0.14, 'my': 0.33, 'ru': 0.06, 'th': 0.19, 'ug': 0.34, 'vi': 0.32, 'zh': 0.0}, 'confidence': 0.55, 'duration': 5.18}
2025-07-08 07:43:00.752 | INFO  | modules.connect:on_check    :557 - client_id:333 - >>> [解析] 第14个数据包, 累计帧数: 590
2025-07-08 07:43:01.282 | INFO  | modules.connect:on_result   :366 - client_id:333 - <<< [发送] 第2个数据包, 更新识别结果: "the city is also the base to climb the niragango vol"
2025-07-08 07:43:01.286 | INFO  | modules.connect:on_check    :557 - client_id:111 - >>> [解析] 第13个数据包, 累计帧数: 556
2025-07-08 07:43:01.297 | INFO  | modules.connect:on_check    :557 - client_id:333 - >>> [解析] 第15个数据包, 累计帧数: 628
2025-07-08 07:43:01.299 | INFO  | modules.connect:on_check    :557 - client_id:111 - >>> [解析] 第14个数据包, 累计帧数: 558
2025-07-08 07:43:01.784 | INFO  | modules.decoder:detokenize  :516 - 话音检测: 0.75 s 添加分隔符, 
2025-07-08 07:43:01.787 | INFO  | modules.connect:on_decode   :856 - client_id:111 - *** 最后一个数据包完成解码 ***
2025-07-08 07:43:01.788 | INFO  | modules.connect:on_result   :366 - client_id:111 - <<< [发送] 第1个数据包, 更新识别结果: "the surface of the moon is made of rocks and dusts the outer layer of the moon is called the crust"
2025-07-08 07:43:01.791 | INFO  | server :receive     :313 - client_id:111 - 已发送最后一个识别结果, 主动关闭客户连接
2025-07-08 07:43:01.792 | INFO  | server :receive     :320 - client_id: 111 - 关闭连接，清理资源
2025-07-08 07:43:01.792 | INFO  | modules.connect:disconnect  :269 - 关闭 ws 连接
2025-07-08 07:43:01.811 | INFO  | modules.connect:on_check    :557 - client_id:333 - >>> [解析] 第16个数据包, 累计帧数: 666
2025-07-08 07:43:02.123 | INFO  | modules.connect:_perform_lid:724 - client_id:777 - LID尝试 6: 语种=en, {'predict': 'en', 'scores': {'ar': 0.3, 'en': 1.0, 'hin': 0.2, 'id': 0.16, 'ja': 0.58, 'kk': 0.38, 'kkin': 0.24, 'ko': 0.24, 'ms': 0.0, 'my': 0.26, 'ru': 0.25, 'th': 0.08, 'ug': 0.22, 'vi': 0.28, 'zh': 0.28}, 'confidence': 0.42, 'duration': 4.42}
2025-07-08 07:43:02.123 | INFO  | modules.connect:_perform_lid:741 - client_id:777 - LID结果确认: 语种=en, 置信度=0.42
2025-07-08 07:43:02.124 | INFO  | modules.asr_manager:switch_to_language:176 - 成功切换到语种: en
2025-07-08 07:43:02.124 | INFO  | modules.connect:_switch_asr_model:799 - client_id:777 - 成功切换到语种: en
2025-07-08 07:43:02.132 | INFO  | modules.connect:on_check    :557 - client_id:777 - >>> [解析] 第13个数据包, 累计帧数: 558
2025-07-08 07:43:02.133 | INFO  | modules.connect:_init_decoder:121 - client_id:777 - 初始化解码器, 使用语种: en
2025-07-08 07:43:02.136 | INFO  | modules.connect:_init_decoder:135 - client_id:777 - 解码器初始化完成, 分隔符: ", "
2025-07-08 07:43:02.137 | INFO  | modules.connect:on_decode   :839 - client_id:777 - 延迟初始化解码器完成
2025-07-08 07:43:05.085 | INFO  | modules.connect:on_result   :366 - client_id:777 - <<< [发送] 第0个数据包, 更新识别结果: "aces e pc earlier launched worldwide for cost"
2025-07-08 07:43:05.091 | INFO  | modules.connect:on_check    :557 - client_id:333 - >>> [解析] 第17个数据包, 累计帧数: 704
2025-07-08 07:43:05.690 | INFO  | modules.connect:on_result   :366 - client_id:333 - <<< [发送] 第3个数据包, 更新识别结果: "the city is also the base to climb the niragango volcano along with some"
2025-07-08 07:43:05.705 | INFO  | modules.connect:on_check    :557 - client_id:777 - >>> [解析] 第14个数据包, 累计帧数: 596
2025-07-08 07:43:06.321 | INFO  | modules.connect:on_result   :366 - client_id:777 - <<< [发送] 第1个数据包, 更新识别结果: "aces e pc earlier launched worldwide for cost saving functionality"
2025-07-08 07:43:06.330 | INFO  | modules.connect:on_check    :557 - client_id:333 - >>> [解析] 第18个数据包, 累计帧数: 742
2025-07-08 07:43:06.338 | INFO  | modules.connect:on_check    :557 - client_id:777 - >>> [解析] 第15个数据包, 累计帧数: 634
2025-07-08 07:43:06.339 | INFO  | modules.decoder:__del__     :411 - ASRDecoder 显式释放资源
2025-07-08 07:43:06.340 | INFO  | modules.decoder:__del__     :149 - Encoder 显式释放资源
2025-07-08 07:43:06.341 | INFO  | modules.decoder:__del__     :267 - CTCPrefixBeamSearch 显式释放资源
2025-07-08 07:43:06.344 | INFO  | modules.connect:on_check    :557 - client_id:333 - >>> [解析] 第19个数据包, 累计帧数: 780
2025-07-08 07:43:06.911 | INFO  | modules.connect:on_result   :366 - client_id:333 - <<< [发送] 第4个数据包, 更新识别结果: "the city is also the base to climb the niragango volcano along with some of the cheapest"
2025-07-08 07:43:06.916 | INFO  | modules.connect:on_check    :557 - client_id:777 - >>> [解析] 第16个数据包, 累计帧数: 672
2025-07-08 07:43:06.920 | INFO  | modules.connect:on_check    :557 - client_id:333 - >>> [解析] 第20个数据包, 累计帧数: 818
2025-07-08 07:43:06.928 | INFO  | modules.connect:on_check    :557 - client_id:777 - >>> [解析] 第17个数据包, 累计帧数: 710
2025-07-08 07:43:07.509 | INFO  | modules.connect:on_result   :366 - client_id:777 - <<< [发送] 第2个数据包, 更新识别结果: "aces e pc earlier launched worldwide for cost saving functionality factors became a"
2025-07-08 07:43:07.929 | INFO  | modules.connect:_perform_lid:724 - client_id:555 - LID尝试 6: 语种=en, {'predict': 'en', 'scores': {'ar': 0.0, 'en': 1.0, 'hin': 0.44, 'id': 0.38, 'ja': 0.43, 'kk': 0.53, 'kkin': 0.29, 'ko': 0.14, 'ms': 0.41, 'my': 0.28, 'ru': 0.23, 'th': 0.12, 'ug': 0.39, 'vi': 0.25, 'zh': 0.0}, 'confidence': 0.47, 'duration': 5.84}
2025-07-08 07:43:07.930 | INFO  | modules.connect:_perform_lid:741 - client_id:555 - LID结果确认: 语种=en, 置信度=0.47
2025-07-08 07:43:07.930 | INFO  | modules.asr_manager:switch_to_language:176 - 成功切换到语种: en
2025-07-08 07:43:07.930 | INFO  | modules.connect:_switch_asr_model:799 - client_id:555 - 成功切换到语种: en
2025-07-08 07:43:07.943 | INFO  | modules.connect:on_check    :557 - client_id:555 - >>> [解析] 第15个数据包, 累计帧数: 616
2025-07-08 07:43:07.943 | INFO  | modules.connect:_init_decoder:121 - client_id:555 - 初始化解码器, 使用语种: en
2025-07-08 07:43:07.944 | INFO  | modules.connect:_init_decoder:135 - client_id:555 - 解码器初始化完成, 分隔符: ", "
2025-07-08 07:43:07.944 | INFO  | modules.connect:on_decode   :839 - client_id:555 - 延迟初始化解码器完成
2025-07-08 07:43:08.221 | INFO  | modules.decoder:detokenize  :516 - 话音检测: 0.75 s 添加分隔符, 
2025-07-08 07:43:08.732 | INFO  | modules.decoder:detokenize  :516 - 话音检测: 0.75 s 添加分隔符, 
2025-07-08 07:43:09.262 | INFO  | modules.decoder:detokenize  :516 - 话音检测: 0.75 s 添加分隔符, 
2025-07-08 07:43:09.807 | INFO  | modules.decoder:detokenize  :516 - 话音检测: 0.75 s 添加分隔符, 
2025-07-08 07:43:10.342 | INFO  | modules.decoder:detokenize  :516 - 话音检测: 0.75 s 添加分隔符, 
2025-07-08 07:43:10.878 | INFO  | modules.decoder:detokenize  :516 - 话音检测: 0.75 s 添加分隔符, 
2025-07-08 07:43:11.242 | INFO  | modules.decoder:detokenize  :516 - 话音检测: 0.75 s 添加分隔符, 
2025-07-08 07:43:11.243 | INFO  | modules.connect:on_decode   :856 - client_id:555 - *** 最后一个数据包完成解码 ***
2025-07-08 07:43:11.243 | INFO  | modules.connect:on_result   :366 - client_id:555 - <<< [发送] 第0个数据包, 更新识别结果: "science hope to understand how planets form especially how the earth forms since comets collided with the earth long ago"
2025-07-08 07:43:11.244 | INFO  | server :receive     :313 - client_id:555 - 已发送最后一个识别结果, 主动关闭客户连接
2025-07-08 07:43:11.245 | INFO  | server :receive     :320 - client_id: 555 - 关闭连接，清理资源
2025-07-08 07:43:11.245 | INFO  | modules.connect:disconnect  :269 - 关闭 ws 连接
2025-07-08 07:43:11.276 | INFO  | modules.connect:on_check    :557 - client_id:333 - >>> [解析] 第21个数据包, 累计帧数: 856
2025-07-08 07:43:11.288 | INFO  | modules.connect:on_check    :557 - client_id:777 - >>> [解析] 第18个数据包, 累计帧数: 748
2025-07-08 07:43:11.294 | INFO  | modules.connect:on_check    :557 - client_id:333 - >>> [解析] 第22个数据包, 累计帧数: 894
2025-07-08 07:43:11.842 | INFO  | modules.connect:on_result   :366 - client_id:333 - <<< [发送] 第5个数据包, 更新识别结果: "the city is also the base to climb the niragango volcano along with some of the cheapest mountain guerrilla traffic"
2025-07-08 07:43:11.854 | INFO  | modules.connect:on_check    :557 - client_id:777 - >>> [解析] 第19个数据包, 累计帧数: 786
2025-07-08 07:43:12.414 | INFO  | modules.connect:on_result   :366 - client_id:777 - <<< [发送] 第3个数据包, 更新识别结果: "aces e pc earlier launched worldwide for cost saving functionality factors became a hot topic in two thousand"
2025-07-08 07:43:12.419 | INFO  | modules.connect:on_check    :557 - client_id:333 - >>> [解析] 第23个数据包, 累计帧数: 932
2025-07-08 07:43:12.425 | INFO  | modules.connect:on_check    :557 - client_id:777 - >>> [解析] 第20个数据包, 累计帧数: 824
2025-07-08 07:43:12.429 | INFO  | modules.connect:on_check    :557 - client_id:333 - >>> [解析] 第24个数据包, 累计帧数: 970
2025-07-08 07:43:13.011 | INFO  | modules.connect:on_result   :366 - client_id:333 - <<< [发送] 第6个数据包, 更新识别结果: "the city is also the base to climb the niragango volcano along with some of the cheapest mountain guerrilla trafficking in africa"
2025-07-08 07:43:13.012 | INFO  | modules.decoder:__del__     :411 - ASRDecoder 显式释放资源
2025-07-08 07:43:13.013 | INFO  | modules.decoder:__del__     :149 - Encoder 显式释放资源
2025-07-08 07:43:13.013 | INFO  | modules.decoder:__del__     :267 - CTCPrefixBeamSearch 显式释放资源
2025-07-08 07:43:13.022 | INFO  | modules.connect:on_check    :557 - client_id:777 - >>> [解析] 第21个数据包, 累计帧数: 862
2025-07-08 07:43:13.027 | INFO  | modules.connect:on_check    :557 - client_id:333 - >>> [解析] 第25个数据包, 累计帧数: 1008
2025-07-08 07:43:13.031 | INFO  | modules.connect:on_check    :557 - client_id:777 - >>> [解析] 第22个数据包, 累计帧数: 900
2025-07-08 07:43:13.593 | INFO  | modules.connect:on_result   :366 - client_id:777 - <<< [发送] 第4个数据包, 更新识别结果: "aces e pc earlier launched worldwide for cost saving functionality factors became a hot topic in two thousand and seven tai pi"
2025-07-08 07:43:13.603 | INFO  | modules.connect:on_check    :557 - client_id:333 - >>> [解析] 第26个数据包, 累计帧数: 1046
2025-07-08 07:43:13.609 | INFO  | modules.connect:on_check    :557 - client_id:777 - >>> [解析] 第23个数据包, 累计帧数: 938
2025-07-08 07:43:13.616 | INFO  | modules.connect:on_check    :557 - client_id:333 - >>> [解析] 第27个数据包, 累计帧数: 1084
2025-07-08 07:43:14.220 | INFO  | modules.connect:on_check    :557 - client_id:777 - >>> [解析] 第24个数据包, 累计帧数: 976
2025-07-08 07:43:14.860 | INFO  | modules.connect:on_result   :366 - client_id:777 - <<< [发送] 第5个数据包, 更新识别结果: "aces e pc earlier launched worldwide for cost saving functionality factors became a hot topic in two thousand and seven tai pi it t month"
2025-07-08 07:43:14.865 | INFO  | modules.connect:on_check    :557 - client_id:333 - >>> [解析] 第28个数据包, 累计帧数: 1090
2025-07-08 07:43:15.214 | INFO  | modules.decoder:detokenize  :516 - 话音检测: 0.75 s 添加分隔符, 
2025-07-08 07:43:15.214 | INFO  | modules.connect:on_decode   :856 - client_id:333 - *** 最后一个数据包完成解码 ***
2025-07-08 07:43:15.214 | INFO  | modules.connect:on_result   :366 - client_id:333 - <<< [发送] 第7个数据包, 更新识别结果: "the city is also the base to climb the niragango volcano along with some of the cheapest mountain guerrilla trafficking in africa"
2025-07-08 07:43:15.215 | INFO  | server :receive     :313 - client_id:333 - 已发送最后一个识别结果, 主动关闭客户连接
2025-07-08 07:43:15.215 | INFO  | server :receive     :320 - client_id: 333 - 关闭连接，清理资源
2025-07-08 07:43:15.216 | INFO  | modules.connect:disconnect  :269 - 关闭 ws 连接
2025-07-08 07:43:15.226 | INFO  | modules.connect:on_check    :557 - client_id:777 - >>> [解析] 第25个数据包, 累计帧数: 1014
2025-07-08 07:43:15.784 | INFO  | modules.connect:_perform_lid:724 - client_id:444 - LID尝试 6: 语种=en, {'predict': 'en', 'scores': {'ar': 0.05, 'en': 1.0, 'hin': 0.0, 'id': 0.3, 'ja': 0.36, 'kk': 0.35, 'kkin': 0.25, 'ko': 0.04, 'ms': 0.23, 'my': 0.35, 'ru': 0.05, 'th': 0.13, 'ug': 0.32, 'vi': 0.32, 'zh': 0.0}, 'confidence': 0.64, 'duration': 7.78}
2025-07-08 07:43:15.785 | INFO  | modules.connect:_perform_lid:741 - client_id:444 - LID结果确认: 语种=en, 置信度=0.64
2025-07-08 07:43:15.785 | INFO  | modules.asr_manager:switch_to_language:176 - 成功切换到语种: en
2025-07-08 07:43:15.785 | INFO  | modules.connect:_switch_asr_model:799 - client_id:444 - 成功切换到语种: en
2025-07-08 07:43:15.798 | INFO  | modules.connect:on_check    :557 - client_id:444 - >>> [解析] 第19个数据包, 累计帧数: 778
2025-07-08 07:43:15.798 | INFO  | modules.connect:_init_decoder:121 - client_id:444 - 初始化解码器, 使用语种: en
2025-07-08 07:43:15.802 | INFO  | modules.connect:_init_decoder:135 - client_id:444 - 解码器初始化完成, 分隔符: ", "
2025-07-08 07:43:15.802 | INFO  | modules.connect:on_decode   :839 - client_id:444 - 延迟初始化解码器完成
2025-07-08 07:43:16.821 | INFO  | modules.decoder:detokenize  :516 - 话音检测: 0.75 s 添加分隔符, 
2025-07-08 07:43:17.603 | INFO  | modules.decoder:detokenize  :516 - 话音检测: 0.75 s 添加分隔符, 
2025-07-08 07:43:18.119 | INFO  | modules.decoder:detokenize  :516 - 话音检测: 0.75 s 添加分隔符, 
2025-07-08 07:43:18.639 | INFO  | modules.decoder:detokenize  :516 - 话音检测: 0.75 s 添加分隔符, 
2025-07-08 07:43:19.160 | INFO  | modules.decoder:detokenize  :516 - 话音检测: 0.75 s 添加分隔符, 
2025-07-08 07:43:19.691 | INFO  | modules.decoder:detokenize  :516 - 话音检测: 0.75 s 添加分隔符, 
2025-07-08 07:43:20.209 | INFO  | modules.decoder:detokenize  :516 - 话音检测: 0.75 s 添加分隔符, 
2025-07-08 07:43:20.449 | INFO  | modules.decoder:detokenize  :516 - 话音检测: 0.75 s 添加分隔符, 
2025-07-08 07:43:20.449 | INFO  | modules.connect:on_decode   :856 - client_id:444 - *** 最后一个数据包完成解码 ***
2025-07-08 07:43:20.450 | INFO  | modules.connect:on_result   :366 - client_id:444 - <<< [发送] 第0个数据包, 更新识别结果: "ancient cultures and tribes began to keep them for easy access to milk hair meat and skins"
2025-07-08 07:43:20.451 | INFO  | server :receive     :313 - client_id:444 - 已发送最后一个识别结果, 主动关闭客户连接
2025-07-08 07:43:20.451 | INFO  | server :receive     :320 - client_id: 444 - 关闭连接，清理资源
2025-07-08 07:43:20.451 | INFO  | modules.connect:disconnect  :269 - 关闭 ws 连接
2025-07-08 07:43:20.458 | ERROR | modules.connect:_perform_lid:753 - client_id:777 - LID执行失败: torch.cat(): expected a non-empty list of Tensors
2025-07-08 07:43:20.461 | INFO  | modules.connect:on_check    :557 - client_id:777 - >>> [解析] 第26个数据包, 累计帧数: 1046
2025-07-08 07:43:20.999 | INFO  | modules.decoder:detokenize  :516 - 话音检测: 0.75 s 添加分隔符, 
2025-07-08 07:43:21.000 | INFO  | modules.connect:on_decode   :856 - client_id:777 - *** 最后一个数据包完成解码 ***
2025-07-08 07:43:21.000 | INFO  | modules.connect:on_result   :366 - client_id:777 - <<< [发送] 第6个数据包, 更新识别结果: "aces e pc earlier launched worldwide for cost saving functionality factors became a hot topic in two thousand and seven tai pi it t month"
2025-07-08 07:43:21.001 | INFO  | server :receive     :313 - client_id:777 - 已发送最后一个识别结果, 主动关闭客户连接
2025-07-08 07:43:21.001 | INFO  | server :receive     :320 - client_id: 777 - 关闭连接，清理资源
2025-07-08 07:43:21.001 | INFO  | modules.connect:disconnect  :269 - 关闭 ws 连接
2025-07-08 07:43:21.580 | INFO  | modules.connect:_perform_lid:724 - client_id:222 - LID尝试 5: 语种=en, {'predict': 'en', 'scores': {'ar': 0.0, 'en': 1.0, 'hin': 0.06, 'id': 0.17, 'ja': 0.15, 'kk': 0.28, 'kkin': 0.23, 'ko': 0.05, 'ms': 0.13, 'my': 0.24, 'ru': 0.06, 'th': 0.03, 'ug': 0.14, 'vi': 0.2, 'zh': 0.0}, 'confidence': 0.72, 'duration': 8.22}
2025-07-08 07:43:21.580 | INFO  | modules.connect:_perform_lid:741 - client_id:222 - LID结果确认: 语种=en, 置信度=0.72
2025-07-08 07:43:21.580 | INFO  | modules.asr_manager:switch_to_language:176 - 成功切换到语种: en
2025-07-08 07:43:21.581 | INFO  | modules.connect:_switch_asr_model:799 - client_id:222 - 成功切换到语种: en
2025-07-08 07:43:21.601 | INFO  | modules.connect:on_check    :557 - client_id:222 - >>> [解析] 第20个数据包, 累计帧数: 834
2025-07-08 07:43:21.602 | INFO  | modules.connect:_init_decoder:121 - client_id:222 - 初始化解码器, 使用语种: en
2025-07-08 07:43:21.606 | INFO  | modules.connect:_init_decoder:135 - client_id:222 - 解码器初始化完成, 分隔符: ", "
2025-07-08 07:43:21.606 | INFO  | modules.connect:on_decode   :839 - client_id:222 - 延迟初始化解码器完成
2025-07-08 07:43:22.648 | INFO  | modules.decoder:detokenize  :516 - 话音检测: 0.75 s 添加分隔符, 
2025-07-08 07:43:23.403 | INFO  | modules.decoder:detokenize  :516 - 话音检测: 0.75 s 添加分隔符, 
2025-07-08 07:43:23.933 | INFO  | modules.decoder:detokenize  :516 - 话音检测: 0.75 s 添加分隔符, 
2025-07-08 07:43:24.461 | INFO  | modules.decoder:detokenize  :516 - 话音检测: 0.75 s 添加分隔符, 
2025-07-08 07:43:24.993 | INFO  | modules.decoder:detokenize  :516 - 话音检测: 0.75 s 添加分隔符, 
2025-07-08 07:43:25.522 | INFO  | modules.decoder:detokenize  :516 - 话音检测: 0.75 s 添加分隔符, 
2025-07-08 07:43:26.089 | INFO  | modules.decoder:detokenize  :516 - 话音检测: 0.75 s 添加分隔符, 
2025-07-08 07:43:26.526 | INFO  | modules.decoder:detokenize  :516 - 话音检测: 0.75 s 添加分隔符, 
2025-07-08 07:43:26.527 | INFO  | modules.connect:on_decode   :856 - client_id:222 - *** 最后一个数据包完成解码 ***
2025-07-08 07:43:26.527 | INFO  | modules.connect:on_result   :366 - client_id:222 - <<< [发送] 第0个数据包, 更新识别结果: "swirl the two dry powers together and then with clean wet hands, squeeze them into a ball"
2025-07-08 07:43:26.528 | INFO  | server :receive     :313 - client_id:222 - 已发送最后一个识别结果, 主动关闭客户连接
2025-07-08 07:43:26.528 | INFO  | server :receive     :320 - client_id: 222 - 关闭连接，清理资源
2025-07-08 07:43:26.529 | INFO  | modules.connect:disconnect  :269 - 关闭 ws 连接
2025-07-08 07:43:26.533 | INFO  | modules.decoder:__del__     :411 - ASRDecoder 显式释放资源
2025-07-08 07:43:26.534 | INFO  | modules.decoder:__del__     :149 - Encoder 显式释放资源
2025-07-08 07:43:26.534 | INFO  | modules.decoder:__del__     :267 - CTCPrefixBeamSearch 显式释放资源
2025-07-08 07:43:26.537 | INFO  | modules.decoder:__del__     :411 - ASRDecoder 显式释放资源
2025-07-08 07:43:26.537 | INFO  | modules.decoder:__del__     :149 - Encoder 显式释放资源
2025-07-08 07:43:26.537 | INFO  | modules.decoder:__del__     :267 - CTCPrefixBeamSearch 显式释放资源
2025-07-08 07:43:26.538 | INFO  | modules.decoder:__del__     :411 - ASRDecoder 显式释放资源
2025-07-08 07:43:26.538 | INFO  | modules.decoder:__del__     :149 - Encoder 显式释放资源
2025-07-08 07:43:26.539 | INFO  | modules.decoder:__del__     :267 - CTCPrefixBeamSearch 显式释放资源
2025-07-08 07:43:26.540 | INFO  | modules.decoder:__del__     :411 - ASRDecoder 显式释放资源
2025-07-08 07:43:26.540 | INFO  | modules.decoder:__del__     :149 - Encoder 显式释放资源
2025-07-08 07:43:26.545 | INFO  | modules.decoder:__del__     :267 - CTCPrefixBeamSearch 显式释放资源
2025-07-08 07:43:27.090 | INFO  | modules.connect:_perform_lid:724 - client_id:666 - LID尝试 4: 语种=en, {'predict': 'en', 'scores': {'ar': 0.0, 'en': 1.0, 'hin': 0.02, 'id': 0.18, 'ja': 0.32, 'kk': 0.28, 'kkin': 0.1, 'ko': 0.1, 'ms': 0.19, 'my': 0.21, 'ru': 0.11, 'th': 0.08, 'ug': 0.18, 'vi': 0.18, 'zh': 0.05}, 'confidence': 0.68, 'duration': 5.36}
2025-07-08 07:43:27.565 | INFO  | modules.connect:_perform_lid:724 - client_id:666 - LID尝试 5: 语种=en, {'predict': 'en', 'scores': {'ar': 0.0, 'en': 1.0, 'hin': 0.03, 'id': 0.21, 'ja': 0.29, 'kk': 0.3, 'kkin': 0.1, 'ko': 0.11, 'ms': 0.22, 'my': 0.23, 'ru': 0.13, 'th': 0.07, 'ug': 0.17, 'vi': 0.22, 'zh': 0.05}, 'confidence': 0.7, 'duration': 6.16}
2025-07-08 07:43:27.565 | INFO  | modules.connect:_perform_lid:741 - client_id:666 - LID结果确认: 语种=en, 置信度=0.7
2025-07-08 07:43:27.565 | INFO  | modules.asr_manager:switch_to_language:176 - 成功切换到语种: en
2025-07-08 07:43:27.566 | INFO  | modules.connect:_switch_asr_model:799 - client_id:666 - 成功切换到语种: en
2025-07-08 07:43:27.590 | INFO  | modules.connect:on_check    :557 - client_id:666 - >>> [解析] 第32个数据包, 累计帧数: 1318
2025-07-08 07:43:27.591 | INFO  | modules.connect:_init_decoder:121 - client_id:666 - 初始化解码器, 使用语种: en
2025-07-08 07:43:27.593 | INFO  | modules.connect:_init_decoder:135 - client_id:666 - 解码器初始化完成, 分隔符: ", "
2025-07-08 07:43:27.594 | INFO  | modules.connect:on_decode   :839 - client_id:666 - 延迟初始化解码器完成
2025-07-08 07:43:34.727 | INFO  | modules.connect:on_result   :366 - client_id:666 - <<< [发送] 第0个数据包, 更新识别结果: "similarly by having a shenzhen visa you do not need to apply for visas to each of the shenhen member countries separately and sav"
2025-07-08 07:43:34.743 | INFO  | modules.connect:on_check    :557 - client_id:666 - >>> [解析] 第33个数据包, 累计帧数: 1356
2025-07-08 07:43:35.279 | INFO  | modules.connect:on_result   :366 - client_id:666 - <<< [发送] 第1个数据包, 更新识别结果: "similarly by having a shenzhen visa you do not need to apply for visas to each of the shenhen member countries separately and saving time money and"
2025-07-08 07:43:35.284 | INFO  | modules.connect:on_check    :557 - client_id:666 - >>> [解析] 第34个数据包, 累计帧数: 1394
2025-07-08 07:43:35.289 | INFO  | modules.connect:on_check    :557 - client_id:666 - >>> [解析] 第35个数据包, 累计帧数: 1432
2025-07-08 07:43:35.293 | INFO  | modules.connect:on_check    :557 - client_id:666 - >>> [解析] 第36个数据包, 累计帧数: 1470
2025-07-08 07:43:35.820 | INFO  | modules.connect:on_result   :366 - client_id:666 - <<< [发送] 第2个数据包, 更新识别结果: "similarly by having a shenzhen visa you do not need to apply for visas to each of the shenhen member countries separately and saving time money and paperwork"
2025-07-08 07:43:35.828 | INFO  | modules.connect:on_check    :557 - client_id:666 - >>> [解析] 第37个数据包, 累计帧数: 1472
2025-07-08 07:43:36.179 | INFO  | modules.decoder:detokenize  :516 - 话音检测: 0.75 s 添加分隔符, 
2025-07-08 07:43:36.179 | INFO  | modules.connect:on_decode   :856 - client_id:666 - *** 最后一个数据包完成解码 ***
2025-07-08 07:43:36.180 | INFO  | modules.connect:on_result   :366 - client_id:666 - <<< [发送] 第3个数据包, 更新识别结果: "similarly by having a shenzhen visa, you do not need to apply for visas to each of the shenhen member countries separately and saving time money and paperwork"
2025-07-08 07:43:36.180 | INFO  | server :receive     :313 - client_id:666 - 已发送最后一个识别结果, 主动关闭客户连接
2025-07-08 07:43:36.181 | INFO  | server :receive     :320 - client_id: 666 - 关闭连接，清理资源
2025-07-08 07:43:36.181 | INFO  | modules.connect:disconnect  :269 - 关闭 ws 连接
2025-07-08 07:43:36.188 | INFO  | modules.decoder:__del__     :411 - ASRDecoder 显式释放资源
2025-07-08 07:43:36.189 | INFO  | modules.decoder:__del__     :149 - Encoder 显式释放资源
2025-07-08 07:43:36.190 | INFO  | modules.decoder:__del__     :267 - CTCPrefixBeamSearch 显式释放资源
2025-07-08 07:43:51.168 | INFO  | server :websocket_endpoint:233 - 新建客户连接 "client_id": "777"

2025-07-08 07:43:51.325 | INFO  | modules.connect:on_check    :403 - client_id:777 - 设置自定义分隔符: ", "
2025-07-08 07:43:51.333 | INFO  | modules.connect:_process_silence_and_lid:664 - client_id:777 - 片段0 检测到有效语音, 开始语种识别
2025-07-08 07:43:51.383 | INFO  | modules.connect:_perform_lid:724 - client_id:777 - LID尝试 1: 语种=ja, {'predict': 'ja', 'scores': {'ar': 0.36, 'en': 0.0, 'hin': 0.29, 'id': 0.04, 'ja': 1.0, 'kk': 0.33, 'kkin': 0.3, 'ko': 0.34, 'ms': 0.09, 'my': 0.2, 'ru': 0.33, 'th': 0.34, 'ug': 0.41, 'vi': 0.25, 'zh': 0.59}, 'confidence': 0.41, 'duration': 0.38}
2025-07-08 07:43:51.809 | INFO  | modules.connect:_perform_lid:724 - client_id:777 - LID尝试 2: 语种=ja, {'predict': 'ja', 'scores': {'ar': 0.34, 'en': 0.07, 'hin': 0.2, 'id': 0.07, 'ja': 1.0, 'kk': 0.48, 'kkin': 0.52, 'ko': 0.38, 'ms': 0.0, 'my': 0.12, 'ru': 0.31, 'th': 0.34, 'ug': 0.51, 'vi': 0.23, 'zh': 0.52}, 'confidence': 0.48, 'duration': 0.78}
2025-07-08 07:43:53.908 | INFO  | modules.connect:_perform_lid:724 - client_id:777 - LID尝试 3: 语种=zh, {'predict': 'zh', 'scores': {'ar': 0.62, 'en': 0.09, 'hin': 0.68, 'id': 0.0, 'ja': 0.8, 'kk': 0.23, 'kkin': 0.53, 'ko': 0.56, 'ms': 0.02, 'my': 0.3, 'ru': 0.47, 'th': 0.41, 'ug': 0.49, 'vi': 0.37, 'zh': 1.0}, 'confidence': 0.2, 'duration': 1.62}
2025-07-08 07:43:55.204 | INFO  | modules.connect:_perform_lid:724 - client_id:777 - LID尝试 4: 语种=en, {'predict': 'en', 'scores': {'ar': 0.34, 'en': 1.0, 'hin': 0.22, 'id': 0.09, 'ja': 0.87, 'kk': 0.45, 'kkin': 0.37, 'ko': 0.35, 'ms': 0.0, 'my': 0.22, 'ru': 0.29, 'th': 0.14, 'ug': 0.22, 'vi': 0.2, 'zh': 0.55}, 'confidence': 0.13, 'duration': 2.82}
2025-07-08 07:43:56.075 | INFO  | modules.connect:_perform_lid:724 - client_id:777 - LID尝试 5: 语种=en, {'predict': 'en', 'scores': {'ar': 0.39, 'en': 1.0, 'hin': 0.3, 'id': 0.11, 'ja': 0.8, 'kk': 0.51, 'kkin': 0.38, 'ko': 0.37, 'ms': 0.0, 'my': 0.29, 'ru': 0.25, 'th': 0.12, 'ug': 0.42, 'vi': 0.2, 'zh': 0.46}, 'confidence': 0.2, 'duration': 3.62}
2025-07-08 07:43:56.959 | INFO  | modules.connect:_perform_lid:724 - client_id:777 - LID尝试 6: 语种=en, {'predict': 'en', 'scores': {'ar': 0.3, 'en': 1.0, 'hin': 0.2, 'id': 0.16, 'ja': 0.58, 'kk': 0.38, 'kkin': 0.24, 'ko': 0.24, 'ms': 0.0, 'my': 0.26, 'ru': 0.25, 'th': 0.08, 'ug': 0.22, 'vi': 0.28, 'zh': 0.28}, 'confidence': 0.42, 'duration': 4.42}
2025-07-08 07:43:56.960 | INFO  | modules.connect:_perform_lid:741 - client_id:777 - LID结果确认: 语种=en, 置信度=0.42
2025-07-08 07:43:56.960 | INFO  | modules.asr_manager:switch_to_language:176 - 成功切换到语种: en
2025-07-08 07:43:56.961 | INFO  | modules.connect:_switch_asr_model:799 - client_id:777 - 成功切换到语种: en
2025-07-08 07:43:56.969 | INFO  | modules.connect:on_check    :557 - client_id:777 - >>> [解析] 第13个数据包, 累计帧数: 558
2025-07-08 07:43:56.970 | INFO  | modules.connect:_init_decoder:121 - client_id:777 - 初始化解码器, 使用语种: en
2025-07-08 07:43:56.971 | INFO  | modules.connect:_init_decoder:135 - client_id:777 - 解码器初始化完成, 分隔符: ", "
2025-07-08 07:43:56.971 | INFO  | modules.connect:on_decode   :839 - client_id:777 - 延迟初始化解码器完成
2025-07-08 07:43:59.779 | INFO  | modules.connect:on_result   :366 - client_id:777 - <<< [发送] 第0个数据包, 更新识别结果: "aces e pc earlier launched worldwide for cost"
2025-07-08 07:43:59.787 | INFO  | modules.connect:on_check    :557 - client_id:777 - >>> [解析] 第14个数据包, 累计帧数: 596
2025-07-08 07:44:00.319 | INFO  | modules.connect:on_result   :366 - client_id:777 - <<< [发送] 第1个数据包, 更新识别结果: "aces e pc earlier launched worldwide for cost saving functionality"
2025-07-08 07:44:00.328 | INFO  | modules.connect:on_check    :557 - client_id:777 - >>> [解析] 第15个数据包, 累计帧数: 634
2025-07-08 07:44:00.332 | INFO  | modules.connect:on_check    :557 - client_id:777 - >>> [解析] 第16个数据包, 累计帧数: 672
2025-07-08 07:44:00.337 | INFO  | modules.connect:on_check    :557 - client_id:777 - >>> [解析] 第17个数据包, 累计帧数: 710
2025-07-08 07:44:00.883 | INFO  | modules.connect:on_result   :366 - client_id:777 - <<< [发送] 第2个数据包, 更新识别结果: "aces e pc earlier launched worldwide for cost saving functionality factors became a"
2025-07-08 07:44:00.888 | INFO  | modules.connect:on_check    :557 - client_id:777 - >>> [解析] 第18个数据包, 累计帧数: 748
2025-07-08 07:44:00.896 | INFO  | modules.connect:on_check    :557 - client_id:777 - >>> [解析] 第19个数据包, 累计帧数: 786
2025-07-08 07:44:01.414 | INFO  | modules.connect:on_result   :366 - client_id:777 - <<< [发送] 第3个数据包, 更新识别结果: "aces e pc earlier launched worldwide for cost saving functionality factors became a hot topic in two thousand"
2025-07-08 07:44:01.419 | INFO  | modules.connect:on_check    :557 - client_id:777 - >>> [解析] 第20个数据包, 累计帧数: 824
2025-07-08 07:44:01.423 | INFO  | modules.connect:on_check    :557 - client_id:777 - >>> [解析] 第21个数据包, 累计帧数: 862
2025-07-08 07:44:01.427 | INFO  | modules.connect:on_check    :557 - client_id:777 - >>> [解析] 第22个数据包, 累计帧数: 900
2025-07-08 07:44:01.959 | INFO  | modules.connect:on_result   :366 - client_id:777 - <<< [发送] 第4个数据包, 更新识别结果: "aces e pc earlier launched worldwide for cost saving functionality factors became a hot topic in two thousand and seven tai pi"
2025-07-08 07:44:01.975 | INFO  | modules.connect:on_check    :557 - client_id:777 - >>> [解析] 第23个数据包, 累计帧数: 938
2025-07-08 07:44:01.982 | INFO  | modules.connect:on_check    :557 - client_id:777 - >>> [解析] 第24个数据包, 累计帧数: 976
2025-07-08 07:44:02.543 | INFO  | modules.connect:on_result   :366 - client_id:777 - <<< [发送] 第5个数据包, 更新识别结果: "aces e pc earlier launched worldwide for cost saving functionality factors became a hot topic in two thousand and seven tai pi it t month"
2025-07-08 07:44:02.555 | INFO  | modules.connect:on_check    :557 - client_id:777 - >>> [解析] 第25个数据包, 累计帧数: 1014
2025-07-08 07:44:02.571 | ERROR | modules.connect:_perform_lid:753 - client_id:777 - LID执行失败: torch.cat(): expected a non-empty list of Tensors
2025-07-08 07:44:02.575 | INFO  | modules.connect:on_check    :557 - client_id:777 - >>> [解析] 第26个数据包, 累计帧数: 1046
2025-07-08 07:44:03.061 | INFO  | modules.decoder:detokenize  :516 - 话音检测: 0.75 s 添加分隔符, 
2025-07-08 07:44:03.062 | INFO  | modules.connect:on_decode   :856 - client_id:777 - *** 最后一个数据包完成解码 ***
2025-07-08 07:44:03.062 | INFO  | modules.connect:on_result   :366 - client_id:777 - <<< [发送] 第6个数据包, 更新识别结果: "aces e pc earlier launched worldwide for cost saving functionality factors became a hot topic in two thousand and seven tai pi it t month"
2025-07-08 07:44:03.063 | INFO  | server :receive     :313 - client_id:777 - 已发送最后一个识别结果, 主动关闭客户连接
2025-07-08 07:44:03.063 | INFO  | server :receive     :320 - client_id: 777 - 关闭连接，清理资源
2025-07-08 07:44:03.063 | INFO  | modules.connect:disconnect  :269 - 关闭 ws 连接
2025-07-08 07:44:03.068 | INFO  | modules.decoder:__del__     :411 - ASRDecoder 显式释放资源
2025-07-08 07:44:03.068 | INFO  | modules.decoder:__del__     :149 - Encoder 显式释放资源
2025-07-08 07:44:03.069 | INFO  | modules.decoder:__del__     :267 - CTCPrefixBeamSearch 显式释放资源
