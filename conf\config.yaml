# ASR服务全局配置文件
# 通用配置参数，语种特定的参数在各自的$lang.yaml中

# 服务器配置
server:
  debug: true
  host: "0.0.0.0"
  port: 10080
  heartbeat_interval: 3000  # 心跳间隔（30秒）
  max_connections: 100    # 最大并发连接数（100个）
  connection_timeout: 3000 # 连接超时时间（300秒）

# 音频处理配置
audio:
  # 与客户端协商一致的数据传输协议参数
  valid_sample_rate_list: [44100, 16000, 8000]   # 合法的采样率，若客户请求数据包的音频数据流并非期望采样率16000，则需要在客户请求数据包中带有`sample_rate`参数，可设置此列表中的采样率数值，服务端会转换成期望采样率16000
  expected_sample_rate: 16000   # 期望的采样率，16000，不可更改
  expected_sample_width: 2      # 期望的采样位数，2位宽，不可更改
  expected_sample_channels: 1   # 期望的声道数，单声道，不可更改
  expected_data_size: 12800     # 期望的音频数据流大小，不可更改
  max_packet_interval: 6000     # 等待数据包到达的最大时长，默认6秒，若因网络拥堵等造成数据包到达时间等待过久，服务端会主动断开连接
  # 音频缓存管理
  max_feat_cache: 1000          # 最大特征缓存，默认1000帧，约10秒
  cache_clean_packet_interval: 30   # 缓存清理间隔,默认每30个数据包清理缓存

# LID（语种识别）配置
lid:
  # 是否启用
  enabled: true
  # 模型加载参数
  model_path: "/ws/MODELS/lid_model/lid.onnx"        # 模型路径
  model_config: "/ws/MODELS/lid_model/config.yaml"   # 模型配置文件路径
  dict_path: "/ws/MODELS/lid_model/spk2id.json"      # 词典文件路径
  fp16: false    # 是否使用单精度模型（适合双精度无法满足实时性的情况，达到加速）
  quant: false   # 是否使用量化模型（适合双精度无法满足实时性的情况，达到加速）
  device: cpu     # 可选：cpu/gpu/npu
  device_id: 0    # 当 device: gpu 或 device: npu 时有效
  # 检测参数
  max_attempts: 6             # 语种识别的最大尝试次数，默认6次，累积接收到有效话音之后，认为6次以内可以识别出正确的语种
  confidence_threshold: 0.7   # 置信度阈值，合理范围为[0.5, 0.9], 置信度越高，LID检测越严格，将越频繁尝试语种识别检测
  silence_threshold: 1.0      # 静音阈值，默认1.0秒，若静音时长超过此时长将会重新尝试语种识别

# ONNX会话池配置
onnx_session_pool:
  enabled: true
  max_sessions_per_model: 4    # 每个模型的最大会话数
  session_timeout: 30000       # 会话超时时间（300秒）
  preload_all_languages: true  # 是否预加载所有语种模型

# 性能监控配置
monitoring:
  memory_check_interval: 60    # 内存检查间隔（秒）
  memory_warning_threshold: 80 # 内存警告阈值（百分比）
  memory_critical_threshold: 90 # 内存严重阈值（百分比）
  enable_health_check: true    # 是否启用健康检查
  health_check_port: 8081     # 健康检查端口

# 日志文件配置
logging:
  level: "INFO"            # 默认写入日志的级别
  rotation: "1 day"        # 更新日志文件，每日更新
  retention: "7 days"      # 压缩日志文件，每周压缩
  compression: "zip"       # 压缩日志数据包格式
  max_file_size: "100 MB"  # 日志文件最大文件大小

# 错误处理配置（TODO 此部分参数未使用，不知道有什么作用）
error_handling:
  max_retries: 3
  retry_delay: 1.0  # 重试延迟（秒）
  enable_detailed_errors: true

# 支持的语种列表
supported_languages:
  - zh
  - en
  - ru
  - kk
  - kkin
  - ug

# 多语种模式配置
multi_language:
  default_language: "zh"  # 默认语种
  fallback_language: "zh" # 回退语种