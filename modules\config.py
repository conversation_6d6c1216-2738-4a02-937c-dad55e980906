#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理模块
统一管理全局配置和语种特定配置
"""

import yaml
import argparse
import os
import sys
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, field
from pathlib import Path
from modules.logger import logger  # 交叉引用？？


CONFIG_DIR = os.path.dirname(__file__) + '/../conf/'

@dataclass
class ServerConfig:
    """服务器配置"""
    host: str = "0.0.0.0"
    port: int = 10080
    heartbeat_interval: int = 30
    max_connections: int = 100
    connection_timeout: int = 300
    # 隐藏参数
    debug: bool = False    # 启用debug模式, 则在终端打印详细日志


@dataclass
class AudioConfig:
    """音频处理配置"""
    # 与客户端协商一致的数据传输协议参数
    valid_sample_rate_list: List[int] = field(default_factory=lambda: [44100, 16000, 8000])
    expected_sample_rate: int = 16000       # 期望的采样率, 16000
    expected_sample_width: int = 2          # 期望的采样位数, 2位宽
    expected_sample_channels: int = 1       # 期望的声道数, 单声道
    expected_data_size: int = 12800         # 期望的音频数据流大小
    max_packet_interval: int = 6            # 等待数据包到达的最大时长, 默认6秒, 若因网络拥堵等造成数据包到达时间等待过久, 服务端会主动断开连接
    # 音频缓存管理参数
    max_feat_cache: int = 1000              # 最大特征缓存, 默认1000帧, 约10秒
    cache_clean_packet_interval: int = 30   # 缓存清理间隔,默认每30个数据包清理缓存


@dataclass
class LIDConfig:
    """语种识别配置"""
    #  是否启用
    enabled: bool = True
    # 模型加载的参数
    model_path: str = "/ws/MODELS/lid_model/lid.onnx"
    model_config: str = "/ws/MODELS/lid_model/config.yaml"
    dict_path: str = "/ws/MODELS/lid_model/spk2id.json"
    do_cmvn: bool = True       # `cmvn_file`` 在 model_config 中定义, 若没有定义, 自动查找 model_path 下的 `global_cmvn`
    quant: bool = False
    fp16: bool = False
    device: str = "cpu"
    device_id: int = 0
    # 隐藏参数
    detection_start: float = 0.4        # 从0.4秒开始LID预测
    detection_interval: float = 0.8     # 未确认语种时, 每隔0.8秒检测一次语种
    # 检测细节控制参数
    max_attempts: int = 6               # 最多尝试6次LID （0.4 + 0.8 * （6-1） = 4.4秒, 认为4.4秒有效话音时可以确认语种）
    confidence_threshold: float = 0.7   # 置信度阈值（置信度定义为模型预测第一名与第二名的差值）大于此阈值立即确认语种
    silence_threshold: int = 1.0        # 连续静音时长超过此值（秒）, 则重置LID, 重新开始检测语种

    def __post_init__(self):
        if self.device not in ['cpu', 'gpu', 'npu']:
            raise ValueError("Device must in ['cpu', 'gpu', 'npu']")
        if self.confidence_threshold > 0.9 or self.confidence_threshold < 0.5:
            raise ValueError(f"lid.confidence_threshold: {self.confidence_threshold} not valid, set value in [0.5, 1.0]")

@dataclass
class ONNXSessionPoolConfig:
    """ONNX会话池配置"""
    enabled: bool = True
    max_sessions_per_model: int = 4
    session_timeout: int = 300
    preload_all_languages: bool = True


@dataclass
class MonitoringConfig:
    """性能监控配置"""
    memory_check_interval: int = 60
    memory_warning_threshold: int = 80
    memory_critical_threshold: int = 90
    enable_health_check: bool = True
    health_check_port: int = 8081

@dataclass
class LoggingConfig:
    """日志配置"""
    level: str = "INFO"
    rotation: str = "1 day"
    retention: str = "7 days"
    compression: str = "zip"
    max_file_size: str = "100 MB"   # 日志文件最大大小，优先于rotation时间

@dataclass
class ErrorConfig:
    """错误处理配置"""
    max_retries: int = 3  # 重试次数（暂未使用，实时ASR不适合重试）
    retry_delay: float = 1.0  # 重试延迟（暂未使用）
    enable_detailed_errors: bool = True  # 是否向客户端发送详细错误信息

@dataclass
class DecodeConfig:
    """通用的模型解码配置 优先使用ONNX模型元数据, 若没有, 则使用通用的模型解码配置"""
    # 均为隐藏参数
    chunk_size: int = 16     # 解码块大小
    num_left_chunks: int = 16     # 缓存块数量
    reverse_weight: float = 0.5   # 注意力重打分中的反向权重
    decoding_window: int = 67
    subsampling_rate: int = 4
    right_context: int = 7
    mode: str = "ctc_prefix_beam_search"

    def __post_init__(self):
        if self.mode not in ['ctc_greedy_search', 'ctc_prefix_beam_search', 'attention_rescoring']:
            raise ValueError("Mode must in ['ctc_greedy_search', 'ctc_prefix_beam_search', 'attention_rescoring']")

@dataclass
class LangSpecificConfig:
    """每个语种的配置"""
    onnx_dir: str = "/ws/MODELS/online_onnx_zh"
    dict_path: str = ""
    fp16: bool = False
    quant: bool = True
    context_list_path: str = None        # 上下文列表路径
    context_graph_score: float = 40.0    # 上下文图偏置分数
    separator_interval: float = 0.5      # 时点超过该间隔阈值则添加间隔符
    default_separator: str = ', '  # 默认分隔符
    device: str = "cpu"
    device_id: int = 0
    # 后处理
    map_path: str = ""
    lower: bool = False
    remove_spm: bool = False
    # 隐藏参数
    feat_type: str = "fbank"

    def __post_init__(self):
        if self.device not in ['cpu', 'gpu', 'npu']:
            raise ValueError("Device must in ['cpu', 'gpu', 'npu']")

    def build_model_config(self, onnx_metadatas: Dict[str, Any], decode_config: DecodeConfig) -> Dict[str, Any]:
        """
        构建模型配置
        Args:
            onnx_metadatas: ONNX模型元数据
            decode_config: 通用解码配置
        Returns:
            Dict: 完整的模型配置
        """
        try:
            # 加载train.yaml配置文件
            train_config_path = os.path.join(self.onnx_dir, 'train.yaml')
            if not os.path.exists(train_config_path):
                raise FileNotFoundError(f"训练配置文件不存在: {train_config_path}")
            
            with open(train_config_path, 'r', encoding='utf-8') as f:
                train_configs = yaml.safe_load(f)

            feat_conf = train_configs['dataset_conf'][f'{self.feat_type}_conf']
            encoder_conf = train_configs['encoder_conf']

            # ONNX模型元数据
            onnx_encoder_conf = onnx_metadatas['encoder']

            # 构建完整配置
            configs = {
                "feat_configs": {
                    "feat_type": self.feat_type,
                    "num_mel_bins": feat_conf['num_mel_bins'],
                    "frame_length": feat_conf['frame_length'],
                    "frame_shift": feat_conf['frame_shift'],
                    "dither": feat_conf['dither'],
                    "n_fft": 400,
                    "hop_length": 160,
                },
                # decode
                'batch': int(onnx_encoder_conf.get("batch", 1)),
                'chunk_size': int(onnx_encoder_conf.get('chunk_size', decode_config.chunk_size)),
                'left_chunks': int(onnx_encoder_conf.get('left_chunks', decode_config.num_left_chunks)),
                'reverse_weight': float(onnx_encoder_conf.get('reverse_weight', decode_config.reverse_weight)),
                'decoding_window': int(onnx_encoder_conf.get("decoding_window", decode_config.decoding_window)),
                'subsampling_rate': int(onnx_encoder_conf.get("subsampling_rate", decode_config.subsampling_rate)),
                'right_context': int(onnx_encoder_conf.get("right_context", decode_config.right_context)),
                'context_list_path': self.context_list_path,
                'context_graph_score': self.context_graph_score,
                # model arch
                'output_size': int(onnx_encoder_conf.get('output_size', encoder_conf['output_size'])),
                'num_blocks': int(encoder_conf.get('num_blocks', encoder_conf['num_blocks'])),
                'cnn_module_kernel': int(onnx_encoder_conf.get('cnn_module_kernel', encoder_conf['cnn_module_kernel'])),
                'head': int(onnx_encoder_conf.get('head', encoder_conf['attention_heads'])),
                # model dim 
                'feature_size': int(onnx_encoder_conf.get('feature_size', train_configs['input_dim'])),
                'vocab_size': int(onnx_encoder_conf.get('vocab_size', train_configs['output_dim']))
            }

            if logger:
                logger.debug(f"构建的模型配置: {configs}")
            return configs

        except Exception as e:
            if logger:
                logger.error(f"构建模型配置失败: {e}")
            raise

@dataclass
class GlobalConfig:
    """全局配置"""
    server: ServerConfig = field(default_factory=ServerConfig)
    audio: AudioConfig = field(default_factory=AudioConfig)
    lid: LIDConfig = field(default_factory=LIDConfig)
    onnx_session_pool: ONNXSessionPoolConfig = field(default_factory=ONNXSessionPoolConfig)
    monitoring: MonitoringConfig = field(default_factory=MonitoringConfig)
    logging: LoggingConfig = field(default_factory=LoggingConfig)
    error: ErrorConfig = field(default_factory=ErrorConfig)
    decode: DecodeConfig = field(default_factory=DecodeConfig)
    # 语种配置
    supported_languages: List[str] = field(default_factory=lambda: ["zh", "en", "ru", "kk", "kkin", "ug"])
    default_language: str = "zh"
    fallback_language: str = "zh"


class ConfigManager:
    """配置管理器"""

    def __init__(self):
        self.global_config: Optional[GlobalConfig] = None
        self.language_configs: Dict[str, Any] = {}
        self._config_cache: Dict[str, Any] = {}

    def load_global_config(self, config_file: str = "config.yaml") -> GlobalConfig:
        """
        加载全局配置
        Args:
            config_file: 配置文件名称
        Returns:
            GlobalConfig: 全局配置对象
        """
        try:
            config_file = Path(CONFIG_DIR + config_file)
            if not config_file.exists():
                if logger:
                    logger.warning(f"全局配置文件不存在: {config_file}, 使用默认配置")
                self.global_config = GlobalConfig()
                return self.global_config

            with open(config_file, 'r', encoding='utf-8') as f:
                config_data = yaml.safe_load(f)

            # 解析配置数据
            self.global_config = self._parse_global_config(config_data)
            if logger:
                logger.info(f"成功加载全局配置: {config_file}")
            return self.global_config

        except Exception as e:
            if logger:
                logger.error(f"加载全局配置失败: {e}")
            self.global_config = GlobalConfig()
            return self.global_config

    def _parse_global_config(self, config_data: Dict[str, Any]) -> GlobalConfig:
        """解析全局配置数据"""
        config = GlobalConfig()

        # 解析服务器配置
        if 'server' in config_data:
            server_data = config_data['server']
            config.server = ServerConfig(
                host=server_data.get('host', config.server.host),
                heartbeat_interval=server_data.get('heartbeat_interval', config.server.heartbeat_interval),
                max_connections=server_data.get('max_connections', config.server.max_connections),
                connection_timeout=server_data.get('connection_timeout', config.server.connection_timeout),
                debug=server_data.get('debug', config.server.debug),
            )

        # 解析音频配置
        if 'audio' in config_data:
            audio_data = config_data['audio']
            config.audio = AudioConfig(
                valid_sample_rate_list=audio_data.get('valid_sample_rate_list', config.audio.valid_sample_rate_list),
                expected_sample_rate=audio_data.get('expected_sample_rate', config.audio.expected_sample_rate),
                expected_sample_width=audio_data.get('expected_sample_width', config.audio.expected_sample_width),
                expected_sample_channels=audio_data.get('expected_sample_channels', config.audio.expected_sample_channels),
                expected_data_size=audio_data.get('expected_data_size', config.audio.expected_data_size),
                max_packet_interval=audio_data.get('max_packet_interval', config.audio.max_packet_interval),
                max_feat_cache=audio_data.get('max_feat_cache', config.audio.max_feat_cache),
                cache_clean_packet_interval=audio_data.get('cache_clean_packet_interval', config.audio.cache_clean_packet_interval)
            )

        # 解析LID配置
        if 'lid' in config_data:
            lid_data = config_data['lid']
            config.lid = LIDConfig(
                enabled=lid_data.get('enabled', config.lid.enabled),
                model_path=lid_data.get('model_path', config.lid.model_path),
                model_config=lid_data.get('model_config', config.lid.model_config),
                dict_path=lid_data.get('dict_path', config.lid.dict_path),
                do_cmvn=lid_data.get('do_cmvn', config.lid.do_cmvn),
                quant=lid_data.get('quant', config.lid.quant),
                fp16=lid_data.get('fp16', config.lid.fp16),
                device=lid_data.get('device', config.lid.device),
                device_id=lid_data.get('device_id', config.lid.device_id),
                detection_start=lid_data.get('detection_start', config.lid.detection_start),
                detection_interval=lid_data.get('detection_interval', config.lid.detection_interval),
                max_attempts=lid_data.get('max_attempts', config.lid.max_attempts),
                confidence_threshold=lid_data.get('confidence_threshold', config.lid.confidence_threshold),
                silence_threshold=lid_data.get('silence_threshold', config.lid.silence_threshold),
            )

        # 解析ONNX会话池配置
        if 'onnx_session_pool' in config_data:
            pool_data = config_data['onnx_session_pool']
            config.onnx_session_pool = ONNXSessionPoolConfig(
                enabled=pool_data.get('enabled', config.onnx_session_pool.enabled),
                max_sessions_per_model=pool_data.get('max_sessions_per_model', config.onnx_session_pool.max_sessions_per_model),
                session_timeout=pool_data.get('session_timeout', config.onnx_session_pool.session_timeout),
                preload_all_languages=pool_data.get('preload_all_languages', config.onnx_session_pool.preload_all_languages)
            )

        # 解析监控配置
        if 'monitoring' in config_data:
            monitoring_data = config_data['monitoring']
            config.monitoring = MonitoringConfig(
                memory_check_interval=monitoring_data.get('memory_check_interval', config.monitoring.memory_check_interval),
                memory_warning_threshold=monitoring_data.get('memory_warning_threshold', config.monitoring.memory_warning_threshold),
                memory_critical_threshold=monitoring_data.get('memory_critical_threshold', config.monitoring.memory_critical_threshold),
                enable_health_check=monitoring_data.get('enable_health_check', config.monitoring.enable_health_check),
                health_check_port=monitoring_data.get('health_check_port', config.monitoring.health_check_port)
            )

        if 'logging' in config_data:
            logging_data = config_data['logging']
            config.logging = LoggingConfig(
                level=logging_data.get('level', config.logging.level),
                rotation=logging_data.get('rotation', config.logging.rotation),
                retention=logging_data.get('retention', config.logging.retention),
                compression=logging_data.get('compression', config.logging.compression),
                max_file_size=logging_data.get('max_file_size', config.logging.max_file_size)
            )

        if 'error_handling' in config_data:
            error_data = config_data['error_handling']
            config.error = ErrorConfig(
                max_retries=error_data.get('max_retries', config.error.max_retries),
                retry_delay=error_data.get('retry_delay', config.error.retry_delay),
                enable_detailed_errors=error_data.get('enable_detailed_errors', config.error.enable_detailed_errors)
            )

        if 'decode' in config_data:
            decode_data = config_data['decode']
            config.decode = DecodeConfig(
                chunk_size=decode_data.get('chunk_size', config.decode.chunk_size),
                num_left_chunks=decode_data.get('num_left_chunks', config.decode.num_left_chunks),
                reverse_weight=decode_data.get('reverse_weight', config.decode.reverse_weight),
                mode=decode_data.get('mode', config.decode.mode)
            )

        # 解析其他配置
        config.supported_languages = config_data.get('supported_languages', config.supported_languages)
        if 'multi_language' in config_data:
            ml_data = config_data['multi_language']
            config.default_language = ml_data.get('default_language', config.default_language)
            config.fallback_language = ml_data.get('fallback_language', config.fallback_language)

        return config


    def load_language_config(self, lang_code: str) -> Dict[str, Any]:
        """
        加载语种特定配置
        Args:
            lang_code: 语种代码
        Returns:
            Dict: 语种配置
        """
        lang_config = LangSpecificConfig()

        if lang_code in self._config_cache:
            return self._config_cache[lang_code]

        try:
            if lang_code == 'multi':
                # 多语种模式, 使用中文配置作为基础配置
                config_file = f"zh.yaml"
            else:
                config_file = f"{lang_code}.yaml"

            config_path = Path(CONFIG_DIR + config_file)
            if not config_path.exists():
                # 尝试在脚本目录下查找
                script_dir = Path(sys.argv[0]).parent
                config_path = script_dir / config_file

            if not config_path.exists():
                raise FileNotFoundError(f"配置文件不存在: {config_file}")

            if logger:
                logger.info(f"加载语种配置: {config_path}")
            with open(config_path, 'r', encoding='utf-8') as f:
                params = yaml.safe_load(f)

            # 解析语言特定的配置
            onnx_dir = params.get('onnx_dir', lang_config.onnx_dir)
            if 'dict_path' not in params:
                dict_path = onnx_dir + '/units.txt'
            lang_config = LangSpecificConfig(
                onnx_dir=onnx_dir,
                dict_path=dict_path,
                fp16=params.get('fp16', lang_config.fp16),
                quant=params.get('quant', lang_config.quant),
                context_list_path=params.get('context_list_path', lang_config.context_list_path),
                context_graph_score=params.get('context_graph_score', lang_config.context_graph_score),
                separator_interval=params.get('separator_interval', lang_config.separator_interval),
                default_separator=params.get('fp16', lang_config.default_separator),
                feat_type=params.get('feat_type', lang_config.feat_type),
                device=params.get('device', lang_config.device),
                device_id=params.get('device_id', lang_config.device_id),
                map_path=params.get('map_path', lang_config.map_path),
                lower=params.get('lower', lang_config.lower),
                remove_spm=params.get('remove_spm', lang_config.remove_spm),
            )

            # 缓存配置
            self._config_cache[lang_code] = lang_config
            self.language_configs[lang_code] = lang_config

            return lang_config

        except Exception as e:
            if logger:
                logger.error(f"加载语种配置失败 {lang_code}: {e}")
            raise

    def get_merged_config(self, lang_code: str) -> Dict[str, Any]:
        """
        获取合并后的配置（全局配置 + 语种配置）
        Args:
            lang_code: 语种代码, 支持 'multi' 或具体语种代码
        Returns:
            Dict: 合并后的配置
        """
        if self.global_config is None:
            self.load_global_config()

        # 构建基础配置结构
        merged_config = {
            'global_config': self.global_config,
            'is_multi_lang': lang_code == 'multi',
            'target_languages': self.global_config.supported_languages if lang_code == 'multi' else [lang_code]
        }

        # 添加全局配置的各个部分（保持向后兼容）
        merged_config.update({
            'server': self.global_config.server,
            'audio': self.global_config.audio,
            'lid': self.global_config.lid,
            'onnx_session_pool': self.global_config.onnx_session_pool,
            'monitoring': self.global_config.monitoring,
            'logging': self.global_config.logging,
            'error': self.global_config.error,
            'decode': self.global_config.decode,
            'default_language': self.global_config.default_language,
            'fallback_language': self.global_config.fallback_language,
            'supported_languages': self.global_config.supported_languages,
        })

        # 添加语种特定配置
        merged_config['model'] = {}
        for l in merged_config['target_languages']:
            try:
                lang_config = self.load_language_config(l)
                merged_config['model'][l] = lang_config
            except Exception as e:
                if logger:
                    logger.warning(f"加载语种 {l} 配置失败: {e}")
                # 对于多语种模式, 跳过失败的语种；对于单语种模式, 抛出异常
                if not merged_config['is_multi_lang']:
                    raise

        return merged_config


# 全局配置管理器实例
config_manager = ConfigManager()


# read_params函数已移除, 现在统一通过ASR管理器获取配置

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description="在线ASR服务",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
            使用示例:
            单语种模式: python server.py zh
            多语种模式: python server.py multi
        """
    )
    # 必要参数
    parser.add_argument('lang_code',
                        default='multi',
                        choices=['zh', 'en', 'ru', 'kk', 'kkin', 'ug', 'multi'],
                        help='语种代码')
    args = parser.parse_args()
    args.multi_lang = (args.lang_code == 'multi')

    try:
        # 加载全局配置
        config_manager.load_global_config()

        # 加载语种特定的配置
        params = config_manager.get_merged_config(args.lang_code)

        # 合并参数
        for key, value in params.items():
            setattr(args, key, value)

        # 验证配置
        _validate_args(args)

        return args

    except Exception as e:
        if logger:
            logger.error(f"配置解析失败: {e}")
        raise


def _validate_args(args) -> None:
    """验证配置参数"""
    # 验证端口范围
    if not (1024 <= args.server.port <= 65535):
        raise ValueError(f"端口号无效: {args.server.port}, 应在1024-65535范围内")

    # 验证模型目录
    for lang_code, lang_config in args.model.items():
        if not os.path.exists(lang_config.onnx_dir):
            raise FileNotFoundError(f"语种 {lang_code} 的模型目录不存在: {lang_config.onnx_dir}")

    # 验证LID模型路径（如果启用）
    if args.lid.enabled and args.lid.model_path and not os.path.exists(args.lid.model_path):
        if logger:
            logger.warning(f"LID模型路径不存在: {args.lid.model_path}")

    if logger:
        logger.info("配置参数验证通过")


class ModuleManager:
    """
    统一的模块初始化管理器
    确保所有模块都按照配置参数进行初始化
    """

    def __init__(self, global_config: GlobalConfig):
        self.global_config = global_config
        self._logger = None
        self._monitoring = None
        self._session_pool = None
        self._initialized_modules = set()

    def init_logger(self, lang_code: str = None, debug_mode: bool = False):
        """初始化日志系统"""
        global logger

        if 'logger' in self._initialized_modules:
            return self._logger

        try:
            from modules.logger import init_logger_from_config

            # 使用配置参数初始化logger
            log_config = {
                'level': self.global_config.logging.level,
                'rotation': self.global_config.logging.rotation,
                'retention': self.global_config.logging.retention,
                'compression': self.global_config.logging.compression,
                'max_file_size': self.global_config.logging.max_file_size
            }

            self._logger = init_logger_from_config(log_config, lang_code, debug_mode)
            logger = self._logger  # 设置全局logger
            self._initialized_modules.add('logger')

            logger.info(f"日志系统初始化成功, 配置: {log_config}")
            return self._logger

        except Exception as e:
            print(f"日志系统初始化失败: {e}")
            # 使用默认logger作为fallback
            from modules.logger import get_default_logger
            self._logger = get_default_logger(debug_mode)
            logger = self._logger
            return self._logger

    def init_monitoring(self):
        """初始化监控系统"""
        if 'monitoring' in self._initialized_modules:
            return self._monitoring

        try:
            from modules.monitoring import initialize_global_monitor

            # 使用配置参数初始化监控系统
            monitoring_config = {
                'enable_health_check': self.global_config.monitoring.enable_health_check,
                'memory_check_interval': self.global_config.monitoring.memory_check_interval,
                'memory_warning_threshold': self.global_config.monitoring.memory_warning_threshold,
                'memory_critical_threshold': self.global_config.monitoring.memory_critical_threshold,
                'health_check_port': self.global_config.monitoring.health_check_port
            }

            self._monitoring = initialize_global_monitor(monitoring_config)
            self._initialized_modules.add('monitoring')
            if logger:
                logger.info("监控系统初始化成功")
            return self._monitoring

        except Exception as e:
            if logger:
                logger.error(f"监控系统初始化失败: {e}")
            return None

    def init_session_pool(self):
        """初始化ONNX会话池"""
        if 'session_pool' in self._initialized_modules:
            return self._session_pool

        try:
            from modules.onnx_session_pool import ONNXSessionPool

            # 使用配置参数初始化会话池
            session_pool_config = {
                'enabled': self.global_config.onnx_session_pool.enabled,
                'max_sessions_per_model': self.global_config.onnx_session_pool.max_sessions_per_model,
                'session_timeout': self.global_config.onnx_session_pool.session_timeout
            }

            self._session_pool = ONNXSessionPool(session_pool_config)
            self._initialized_modules.add('session_pool')
            if logger:
                logger.info("ONNX会话池初始化成功")
            return self._session_pool

        except Exception as e:
            if logger:
                logger.error(f"ONNX会话池初始化失败: {e}")
            return None

    def init_all_modules(self, lang_code: str = None, debug_mode: bool = False):
        """初始化所有模块"""
        # 首先初始化日志系统
        self.init_logger(lang_code, debug_mode)

        # 然后初始化其他模块
        self.init_monitoring()
        self.init_session_pool()

        if logger:
            logger.info("所有模块初始化完成")

    def get_logger(self):
        """获取logger实例"""
        return self._logger

    def get_monitoring(self):
        """获取监控系统实例"""
        return self._monitoring

    def get_session_pool(self):
        """获取会话池实例"""
        return self._session_pool


# 全局配置管理器实例
config_manager = ConfigManager()


